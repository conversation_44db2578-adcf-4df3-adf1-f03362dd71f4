#!/bin/bash

# Script to ensure ConfigRegistry is properly initialized and accessible

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${CYAN}║            Ensure ConfigRegistry Initialization              ║${NC}"
echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
echo

# Load deployment addresses
CONFIG_FILE="config/deployments.json"

if [ ! -f "$CONFIG_FILE" ]; then
    echo -e "${RED}Error: ${CONFIG_FILE} not found${NC}"
    exit 1
fi

# Extract addresses from deployments.json
REGISTRY_ID=$(jq -r '.deployments.devnet.registry.registryId' "$CONFIG_FILE")
ADMIN_CAP_ID=$(jq -r '.deployments.devnet.registry.adminCapId' "$CONFIG_FILE")
MEME_CONFIG_ID=$(jq -r '.deployments.devnet.hopfun.memeConfigId' "$CONFIG_FILE")
DEX_CONFIG_ID=$(jq -r '.deployments.devnet.hopdex.dexConfigId' "$CONFIG_FILE")
HOPFUN_PACKAGE=$(jq -r '.deployments.devnet.hopfun.packageId' "$CONFIG_FILE")
HOPDEX_PACKAGE=$(jq -r '.deployments.devnet.hopdex.packageId' "$CONFIG_FILE")
REGISTRY_PACKAGE=$(jq -r '.deployments.devnet.registry.packageId' "$CONFIG_FILE")

echo -e "${CYAN}Step 1: Checking Registry Object${NC}"
echo -e "  Registry ID: ${YELLOW}${REGISTRY_ID}${NC}"

# Check if registry exists and is shared
REGISTRY_CHECK=$(sui client object "$REGISTRY_ID" --json 2>/dev/null)
if [ -z "$REGISTRY_CHECK" ]; then
    echo -e "${RED}❌ Registry object not found!${NC}"
    exit 1
fi

OWNER_TYPE=$(echo "$REGISTRY_CHECK" | jq -r '.owner.Shared // "not_shared"')
if [ "$OWNER_TYPE" = "not_shared" ]; then
    echo -e "${RED}❌ Registry is not a shared object!${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Registry exists and is shared${NC}"
echo -e "  Initial shared version: $(echo "$REGISTRY_CHECK" | jq -r '.owner.Shared.initial_shared_version')"

# Get current registry state
echo
echo -e "${CYAN}Step 2: Current Registry Configuration${NC}"
REGISTRY_FIELDS=$(echo "$REGISTRY_CHECK" | jq -r '.content.fields')

CURRENT_MEME_CONFIG=$(echo "$REGISTRY_FIELDS" | jq -r '.meme_config_address')
CURRENT_DEX_CONFIG=$(echo "$REGISTRY_FIELDS" | jq -r '.dex_config_address')
CURRENT_HOPFUN_PKG=$(echo "$REGISTRY_FIELDS" | jq -r '.hopfun_package_id')
CURRENT_HOPDEX_PKG=$(echo "$REGISTRY_FIELDS" | jq -r '.hopdex_package_id')

echo -e "  MemeConfig:     ${CURRENT_MEME_CONFIG}"
echo -e "  DexConfig:      ${CURRENT_DEX_CONFIG}"
echo -e "  HopFun Package: ${CURRENT_HOPFUN_PKG}"
echo -e "  HopDex Package: ${CURRENT_HOPDEX_PKG}"

# Check if addresses are set to 0x0
if [ "$CURRENT_MEME_CONFIG" = "0x0000000000000000000000000000000000000000000000000000000000000000" ] || [ "$CURRENT_MEME_CONFIG" = "0x0" ]; then
    echo -e "${YELLOW}⚠ MemeConfig is not initialized (set to 0x0)${NC}"
    NEEDS_INIT=true
else
    echo -e "${GREEN}✓ MemeConfig is set${NC}"
    # Verify it matches expected
    if [ "$CURRENT_MEME_CONFIG" != "$MEME_CONFIG_ID" ]; then
        echo -e "${YELLOW}⚠ Warning: MemeConfig doesn't match expected value${NC}"
        echo -e "  Expected: ${MEME_CONFIG_ID}"
        echo -e "  Current:  ${CURRENT_MEME_CONFIG}"
        NEEDS_INIT=true
    fi
fi

# Initialize if needed
if [ "$NEEDS_INIT" = true ]; then
    echo
    echo -e "${CYAN}Step 3: Initializing Registry${NC}"
    echo -e "  Using Admin Cap: ${YELLOW}${ADMIN_CAP_ID}${NC}"
    
    # Check admin cap exists
    ADMIN_CAP_CHECK=$(sui client object "$ADMIN_CAP_ID" --json 2>/dev/null | jq -r '.objectId // "NOT_FOUND"')
    if [ "$ADMIN_CAP_CHECK" = "NOT_FOUND" ]; then
        echo -e "${RED}❌ Admin cap not found!${NC}"
        exit 1
    fi
    
    echo -e "${CYAN}Calling update_all_addresses...${NC}"
    TX_RESULT=$(sui client call \
        --package "$REGISTRY_PACKAGE" \
        --module registry \
        --function update_all_addresses \
        --args \
            "$REGISTRY_ID" \
            "$ADMIN_CAP_ID" \
            "$MEME_CONFIG_ID" \
            "$DEX_CONFIG_ID" \
            "$HOPFUN_PACKAGE" \
            "$HOPDEX_PACKAGE" \
        --gas-budget 10000000 \
        --json 2>&1)
    
    # Check if transaction was successful
    if echo "$TX_RESULT" | jq -e '.effects.status.status == "success"' > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Registry successfully updated!${NC}"
        DIGEST=$(echo "$TX_RESULT" | jq -r '.digest')
        echo -e "  Transaction: ${CYAN}${DIGEST}${NC}"
        
        # Wait for transaction to process
        sleep 2
    else
        echo -e "${RED}❌ Failed to update registry${NC}"
        echo "$TX_RESULT" | jq '.'
        exit 1
    fi
fi

# Final verification
echo
echo -e "${CYAN}Step 4: Final Verification${NC}"

# Re-fetch registry state
FINAL_CHECK=$(sui client object "$REGISTRY_ID" --json 2>/dev/null)
FINAL_FIELDS=$(echo "$FINAL_CHECK" | jq -r '.content.fields')
FINAL_MEME_CONFIG=$(echo "$FINAL_FIELDS" | jq -r '.meme_config_address')

# Verify MemeConfig object exists
echo -e "  Checking MemeConfig object..."
MEME_CONFIG_CHECK=$(sui client object "$FINAL_MEME_CONFIG" --json 2>/dev/null | jq -r '.objectId // "NOT_FOUND"')
if [ "$MEME_CONFIG_CHECK" = "NOT_FOUND" ]; then
    echo -e "${RED}❌ MemeConfig object does not exist on chain!${NC}"
    exit 1
fi
echo -e "${GREEN}✓ MemeConfig object exists${NC}"

# Verify it's a shared object (MemeConfig should be owned by an address)
MEME_CONFIG_OWNER=$(sui client object "$FINAL_MEME_CONFIG" --json 2>/dev/null | jq -r '.owner')
echo -e "  MemeConfig owner: $(echo "$MEME_CONFIG_OWNER" | jq -c '.')"

# Check frontend environment
echo
echo -e "${CYAN}Step 5: Frontend Configuration${NC}"
ENV_FILE="apps/frontend/.env.local"
if [ -f "$ENV_FILE" ]; then
    USE_REGISTRY=$(grep "NEXT_PUBLIC_USE_REGISTRY" "$ENV_FILE" | cut -d'=' -f2)
    if [ "$USE_REGISTRY" = "true" ]; then
        echo -e "${GREEN}✓ Frontend configured to use registry (NEXT_PUBLIC_USE_REGISTRY=true)${NC}"
    else
        echo -e "${YELLOW}⚠ Frontend not configured to use registry${NC}"
        echo -e "  Add 'NEXT_PUBLIC_USE_REGISTRY=true' to $ENV_FILE"
    fi
    
    ENV_REGISTRY_ID=$(grep "NEXT_PUBLIC_REGISTRY_ID_DEVNET" "$ENV_FILE" | cut -d'=' -f2)
    if [ "$ENV_REGISTRY_ID" = "$REGISTRY_ID" ]; then
        echo -e "${GREEN}✓ Registry ID matches in .env.local${NC}"
    else
        echo -e "${YELLOW}⚠ Registry ID mismatch in .env.local${NC}"
        echo -e "  Expected: ${REGISTRY_ID}"
        echo -e "  In .env:  ${ENV_REGISTRY_ID}"
    fi
else
    echo -e "${YELLOW}⚠ Frontend .env.local not found${NC}"
fi

echo
echo -e "${CYAN}═══════════════════════════════════════════════════════════════${NC}"
echo -e "${GREEN}✅ Registry Verification Complete${NC}"
echo -e "${CYAN}═══════════════════════════════════════════════════════════════${NC}"
echo
echo "Registry Details:"
echo "  ID:           $REGISTRY_ID"
echo "  MemeConfig:   $FINAL_MEME_CONFIG"
echo "  Status:       Ready for token creation"
echo
echo "Next steps:"
echo "  1. Restart the frontend if it's running"
echo "  2. Try creating a token again"