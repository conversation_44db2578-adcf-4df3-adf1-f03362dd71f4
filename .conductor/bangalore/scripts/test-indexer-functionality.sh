#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}===================================${NC}"
echo -e "${GREEN}Testing Indexer Functionality${NC}"
echo -e "${GREEN}===================================${NC}"

# Check if MongoDB is running
echo -e "\n${YELLOW}1. Checking MongoDB status...${NC}"
if docker-compose ps | grep -q "mongodb.*Up"; then
    echo -e "${GREEN}✅ MongoDB is running${NC}"
else
    echo -e "${RED}❌ MongoDB is not running. Starting it now...${NC}"
    docker-compose up -d
    sleep 5
fi

# Test MongoDB connection
echo -e "\n${YELLOW}2. Testing MongoDB connection...${NC}"
MONGO_TEST=$(docker exec -it hopfun-mongodb-1 mongosh "*******************************************************************************" --eval "db.adminCommand('ping')" 2>&1)
if echo "$MONGO_TEST" | grep -q "1"; then
    echo -e "${GREEN}✅ MongoDB connection successful${NC}"
else
    echo -e "${RED}❌ MongoDB connection failed${NC}"
    echo "$MONGO_TEST"
    exit 1
fi

# Check if required environment variables are set
echo -e "\n${YELLOW}3. Checking indexer environment configuration...${NC}"
cd apps/indexer

if [ ! -f ".env" ]; then
    echo -e "${RED}❌ .env file not found in apps/indexer${NC}"
    exit 1
fi

# Source the environment file
export $(cat .env | grep -v '^#' | xargs)

# Verify critical environment variables
REQUIRED_VARS=("DATABASE_URL" "SUI_RPC_URL" "HOPFUN_PACKAGE_ID" "HOPDEX_PACKAGE_ID")
for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        echo -e "${RED}❌ Missing required environment variable: $var${NC}"
        exit 1
    else
        echo -e "${GREEN}✅ $var is set${NC}"
    fi
done

# Test Sui RPC connection
echo -e "\n${YELLOW}4. Testing Sui RPC connection...${NC}"
RPC_TEST=$(curl -s -X POST "$SUI_RPC_URL" \
    -H "Content-Type: application/json" \
    -d '{"jsonrpc":"2.0","id":1,"method":"sui_getLatestCheckpointSequenceNumber","params":[]}' | jq -r '.result')

if [ ! -z "$RPC_TEST" ] && [ "$RPC_TEST" != "null" ]; then
    echo -e "${GREEN}✅ Sui RPC connection successful (Latest checkpoint: $RPC_TEST)${NC}"
else
    echo -e "${RED}❌ Failed to connect to Sui RPC${NC}"
    exit 1
fi

# Build the indexer
echo -e "\n${YELLOW}5. Building the indexer...${NC}"
pnpm build
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Indexer built successfully${NC}"
else
    echo -e "${RED}❌ Failed to build indexer${NC}"
    exit 1
fi

# Start the indexer in the background for a short test
echo -e "\n${YELLOW}6. Starting indexer for test run...${NC}"
timeout 10s pnpm start > indexer_test.log 2>&1 &
INDEXER_PID=$!

# Wait a moment for the indexer to start
sleep 3

# Check if indexer is running
if ps -p $INDEXER_PID > /dev/null; then
    echo -e "${GREEN}✅ Indexer started successfully${NC}"
    
    # Check health endpoint
    echo -e "\n${YELLOW}7. Checking indexer health endpoint...${NC}"
    HEALTH_CHECK=$(curl -s -f http://localhost:3001/health 2>/dev/null)
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Health endpoint is responding${NC}"
        echo "Response: $HEALTH_CHECK"
    else
        echo -e "${YELLOW}⚠️ Health endpoint not responding (this may be normal during startup)${NC}"
    fi
else
    echo -e "${RED}❌ Indexer failed to start${NC}"
    echo "Check indexer_test.log for details:"
    tail -20 indexer_test.log
fi

# Clean up
kill $INDEXER_PID 2>/dev/null
wait $INDEXER_PID 2>/dev/null

# Check database for indexed data
echo -e "\n${YELLOW}8. Checking database for indexed data...${NC}"
cd ../..
node -e "
const { PrismaClient } = require('@hopfun/database');
const prisma = new PrismaClient();

async function checkDatabase() {
    try {
        // Check IndexerState
        const indexerState = await prisma.indexerState.findFirst();
        console.log('IndexerState:', indexerState ? 'Found checkpoint: ' + indexerState.lastProcessedCheckpoint : 'Not initialized');
        
        // Count tokens
        const tokenCount = await prisma.token.count();
        console.log('Tokens in database:', tokenCount);
        
        // Count events
        const connectorEvents = await prisma.connectorCreatedEvent.count();
        const bondingCurveEvents = await prisma.bondingCurveCreatedEvent.count();
        const transactions = await prisma.bondingCurveTransaction.count();
        
        console.log('Connector events:', connectorEvents);
        console.log('Bonding curve events:', bondingCurveEvents);
        console.log('Transactions:', transactions);
        
        // Check for recent activity
        const recentToken = await prisma.token.findFirst({
            orderBy: { createdAt: 'desc' },
            include: { stats: true }
        });
        
        if (recentToken) {
            console.log('\\nMost recent token:');
            console.log('- Name:', recentToken.coinName);
            console.log('- Symbol:', recentToken.ticker);
            console.log('- Created:', recentToken.createdAt);
            console.log('- Market Cap:', recentToken.marketCap);
            if (recentToken.stats) {
                console.log('- Volume 24h:', recentToken.stats.volume24h);
                console.log('- Transactions 24h:', recentToken.stats.transactions24h);
            }
        }
        
        await prisma.\$disconnect();
        process.exit(0);
    } catch (error) {
        console.error('Database check failed:', error.message);
        await prisma.\$disconnect();
        process.exit(1);
    }
}

checkDatabase();
" 2>&1

echo -e "\n${GREEN}===================================${NC}"
echo -e "${GREEN}Indexer functionality test complete!${NC}"
echo -e "${GREEN}===================================${NC}"

# Display log file location
echo -e "\n${YELLOW}Indexer test log saved to: apps/indexer/indexer_test.log${NC}"
echo -e "${YELLOW}To run the indexer continuously: cd apps/indexer && pnpm start${NC}"