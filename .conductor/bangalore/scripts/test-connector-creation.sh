#!/bin/bash

# Test script to verify connector creation works with the registry

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${CYAN}║              Test Connector Creation with Registry           ║${NC}"
echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
echo

# Load configuration
CONFIG_FILE="config/deployments.json"
REGISTRY_ID=$(jq -r '.deployments.devnet.registry.registryId' "$CONFIG_FILE")

echo -e "${CYAN}Testing registry accessibility...${NC}"

# First, check if we can read the registry
REGISTRY_DATA=$(sui client object "$REGISTRY_ID" --json 2>/dev/null)
if [ -z "$REGISTRY_DATA" ]; then
    echo -e "${RED}❌ Cannot access registry object${NC}"
    exit 1
fi

# Extract registry fields
MEME_CONFIG=$(echo "$REGISTRY_DATA" | jq -r '.content.fields.meme_config_address')
echo -e "  Registry ID:    ${GREEN}$REGISTRY_ID${NC}"
echo -e "  MemeConfig:     ${GREEN}$MEME_CONFIG${NC}"

# Build a test coin_template and try to publish it
echo
echo -e "${CYAN}Building test coin module...${NC}"
cd contracts/coin_template

# Build the module
sui move build --skip-fetch-latest-git-deps 2>&1 | tail -5

echo
echo -e "${CYAN}Test Summary:${NC}"
echo -e "  Registry:        ${GREEN}✓ Accessible${NC}"
echo -e "  MemeConfig:      ${GREEN}✓ Set to $MEME_CONFIG${NC}"
echo -e "  Coin Template:   ${GREEN}✓ Builds successfully${NC}"

echo
echo -e "${YELLOW}Important Notes:${NC}"
echo "1. The registry is properly configured"
echo "2. If token creation still fails, try:"
echo "   - Restart the frontend server to pick up NEXT_PUBLIC_USE_REGISTRY=true"
echo "   - Clear browser cache and local storage"
echo "   - Check browser console for detailed error messages"
echo
echo -e "${CYAN}Frontend restart command:${NC}"
echo "  pkill -f 'next-server' && pnpm run dev:frontend"