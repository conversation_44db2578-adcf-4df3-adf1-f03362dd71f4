// MongoDB initialization script
// This script runs when the MongoDB container starts for the first time

// Helper function to create indexes with error handling
function createIndexSafely(collection, indexSpec, options = {}) {
  try {
    collection.createIndex(indexSpec, options);
    print(`Successfully created index on ${collection.getName()}: ${JSON.stringify(indexSpec)}`);
  } catch (e) {
    print(`Warning: Could not create index on ${collection.getName()}: ${e.message}`);
  }
}

// Switch to the hopfun database
db = db.getSiblingDB('hopfun');

// Create collections with validation schemas
// ==========================================

// Tokens collection
db.createCollection('tokens', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['address', 'symbol', 'name', 'createdAt'],
      properties: {
        address: {
          bsonType: 'string',
          description: 'Token contract address'
        },
        symbol: {
          bsonType: 'string',
          maxLength: 10,
          description: 'Token symbol'
        },
        name: {
          bsonType: 'string',
          maxLength: 100,
          description: 'Token name'
        },
        decimals: {
          bsonType: 'int',
          minimum: 0,
          maximum: 18,
          description: 'Token decimals'
        },
        totalSupply: {
          bsonType: 'string',
          description: 'Total token supply'
        },
        createdAt: {
          bsonType: 'date',
          description: 'Creation timestamp'
        },
        updatedAt: {
          bsonType: 'date',
          description: 'Last update timestamp'
        }
      }
    }
  }
});

// Token stats collection
db.createCollection('tokenStats', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['tokenId', 'volumeUSD24h', 'transactionCount24h'],
      properties: {
        tokenId: {
          bsonType: 'objectId',
          description: 'Reference to token'
        },
        volumeUSD24h: {
          bsonType: 'decimal',
          minimum: 0,
          description: '24h trading volume in USD'
        },
        transactionCount24h: {
          bsonType: 'int',
          minimum: 0,
          description: '24h transaction count'
        }
      }
    }
  }
});

// User holdings collection
db.createCollection('userHoldings', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['userAddress', 'tokenId', 'balance'],
      properties: {
        userAddress: {
          bsonType: 'string',
          description: 'User wallet address'
        },
        tokenId: {
          bsonType: 'objectId',
          description: 'Reference to token'
        },
        balance: {
          bsonType: 'string',
          description: 'Token balance'
        }
      }
    }
  }
});

// Token transactions collection
db.createCollection('tokenTransactions', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['tokenId', 'type', 'timestamp', 'transactionHash'],
      properties: {
        tokenId: {
          bsonType: 'objectId',
          description: 'Reference to token'
        },
        type: {
          enum: ['BUY', 'SELL', 'TRANSFER'],
          description: 'Transaction type'
        },
        timestamp: {
          bsonType: 'date',
          description: 'Transaction timestamp'
        },
        transactionHash: {
          bsonType: 'string',
          description: 'Blockchain transaction hash'
        }
      }
    }
  }
});

// Indexer state collection
db.createCollection('indexerState', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['id', 'lastCheckpoint'],
      properties: {
        id: {
          bsonType: 'string',
          description: 'Indexer identifier'
        },
        lastCheckpoint: {
          bsonType: 'long',
          minimum: 0,
          description: 'Last processed checkpoint'
        }
      }
    }
  }
});

// Processing queue collection
db.createCollection('processingQueue', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['eventType', 'eventData', 'status', 'createdAt'],
      properties: {
        eventType: {
          bsonType: 'string',
          description: 'Type of event'
        },
        eventData: {
          bsonType: 'object',
          description: 'Event data'
        },
        status: {
          enum: ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'],
          description: 'Processing status'
        },
        attempts: {
          bsonType: 'int',
          minimum: 0,
          description: 'Processing attempt count'
        },
        createdAt: {
          bsonType: 'date',
          description: 'Queue entry creation time'
        }
      }
    }
  }
});

// Create Indexes for Performance
// ===============================

// Tokens indexes
createIndexSafely(db.tokens, { address: 1 }, { unique: true });
createIndexSafely(db.tokens, { symbol: 1 });
createIndexSafely(db.tokens, { createdAt: -1 });
createIndexSafely(db.tokens, { 'metadata.creator': 1 });

// Token stats indexes
createIndexSafely(db.tokenStats, { tokenId: 1 }, { unique: true });
createIndexSafely(db.tokenStats, { volumeUSD24h: -1 });
createIndexSafely(db.tokenStats, { transactionCount24h: -1 });
createIndexSafely(db.tokenStats, { updatedAt: -1 });

// User holdings indexes
createIndexSafely(db.userHoldings, { userAddress: 1, tokenId: 1 }, { unique: true });
createIndexSafely(db.userHoldings, { userAddress: 1 });
createIndexSafely(db.userHoldings, { tokenId: 1 });
createIndexSafely(db.userHoldings, { balance: -1 });

// Token transactions indexes
createIndexSafely(db.tokenTransactions, { tokenId: 1, timestamp: -1 });
createIndexSafely(db.tokenTransactions, { transactionHash: 1 }, { unique: true });
createIndexSafely(db.tokenTransactions, { type: 1 });
createIndexSafely(db.tokenTransactions, { userAddress: 1 });
createIndexSafely(db.tokenTransactions, { timestamp: -1 });

// Bonding curve events indexes
createIndexSafely(db.bondingCurveCreatedEvents, { memeAddress: 1 }, { unique: true });
createIndexSafely(db.bondingCurveCreatedEvents, { createdAt: -1 });
createIndexSafely(db.bondingCurveTransactions, { memeAddress: 1, timestamp: -1 });
createIndexSafely(db.bondingCurveTransactions, { userAddress: 1 });
createIndexSafely(db.bondingCurveCompleteEvents, { memeAddress: 1 }, { unique: true });
createIndexSafely(db.bondingCurveMigrateEvents, { memeAddress: 1 }, { unique: true });

// Connector events indexes
createIndexSafely(db.connectorCreatedEvents, { connectorId: 1 }, { unique: true });
createIndexSafely(db.connectorCreatedEvents, { memeAddress: 1 });

// Indexer state indexes
createIndexSafely(db.indexerState, { id: 1 }, { unique: true });

// Processing queue indexes
createIndexSafely(db.processingQueue, { status: 1, createdAt: 1 });
createIndexSafely(db.processingQueue, { eventType: 1 });
createIndexSafely(db.processingQueue, { attempts: 1 });

// Create TTL indexes for automatic cleanup
// =========================================
createIndexSafely(
  db.processingQueue, 
  { updatedAt: 1 }, 
  { 
    expireAfterSeconds: 86400 * 7, // Keep completed/failed items for 7 days
    partialFilterExpression: { 
      status: { $in: ['COMPLETED', 'FAILED'] } 
    }
  }
);

// Create text indexes for search functionality
// =============================================
createIndexSafely(db.tokens, { 
  name: 'text', 
  symbol: 'text', 
  'metadata.description': 'text' 
});

// Set up capped collection for system logs (optional)
// ====================================================
db.createCollection('systemLogs', {
  capped: true,
  size: 10485760, // 10MB
  max: 10000
});

// Create initial system configuration
// ====================================
db.systemConfig.insertOne({
  _id: 'config',
  version: '1.0.0',
  features: {
    indexingEnabled: true,
    realtimeUpdates: true,
    metricsCollection: true
  },
  limits: {
    maxBatchSize: 100,
    maxRetries: 3,
    processingDelayMs: 1000
  },
  createdAt: new Date(),
  updatedAt: new Date()
});

// Create application users (if not exists)
// =========================================
const adminDb = db.getSiblingDB('admin');

// Application read-write user
try {
  adminDb.createUser({
    user: 'hopfun_app',
    pwd: 'app_password_change_in_production',
    roles: [
      { role: 'readWrite', db: 'hopfun' },
      { role: 'dbAdmin', db: 'hopfun' }
    ]
  });
  print('Created application user: hopfun_app');
} catch (e) {
  print('Application user might already exist: ' + e.message);
}

// Read-only user for analytics/reporting
try {
  adminDb.createUser({
    user: 'hopfun_readonly',
    pwd: 'readonly_password_change_in_production',
    roles: [
      { role: 'read', db: 'hopfun' }
    ]
  });
  print('Created read-only user: hopfun_readonly');
} catch (e) {
  print('Read-only user might already exist: ' + e.message);
}

// Backup user
try {
  adminDb.createUser({
    user: 'hopfun_backup',
    pwd: 'backup_password_change_in_production',
    roles: [
      { role: 'backup', db: 'admin' },
      { role: 'restore', db: 'admin' }
    ]
  });
  print('Created backup user: hopfun_backup');
} catch (e) {
  print('Backup user might already exist: ' + e.message);
}

print('====================================');
print('MongoDB initialization completed successfully!');
print('Database: hopfun');
print('Collections created with validation schemas');
print('Indexes created for optimal performance');
print('Users created for different access levels');
print('====================================');
print('');
print('Connection strings:');
print('- MongoDB Compass: *************************************************************************');
print('- Application: ****************************************************************************************');
print('- Read-only: **************************************************************************************************');
print('====================================');