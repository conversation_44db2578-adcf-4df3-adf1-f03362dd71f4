#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}===================================${NC}"
echo -e "${GREEN}Starting Indexer Test${NC}"
echo -e "${GREEN}===================================${NC}"

# Navigate to indexer directory
cd apps/indexer

# Check if .env exists
if [ ! -f ".env" ]; then
    echo -e "${RED}❌ .env file not found in apps/indexer${NC}"
    exit 1
fi

echo -e "\n${YELLOW}1. Building the indexer...${NC}"
pnpm build
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Indexer built successfully${NC}"
else
    echo -e "${RED}❌ Failed to build indexer${NC}"
    exit 1
fi

echo -e "\n${YELLOW}2. Starting indexer in test mode...${NC}"
echo -e "${YELLOW}   Running for 30 seconds to check for errors...${NC}"

# Start indexer and capture output
timeout 30s pnpm start 2>&1 | tee indexer_output.log &
INDEXER_PID=$!

# Monitor the output for errors
sleep 5

# Check if process is still running
if ps -p $INDEXER_PID > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Indexer is running${NC}"
    
    # Check for common error patterns in the log
    if grep -q "Error\|Failed\|Cannot connect" indexer_output.log; then
        echo -e "${YELLOW}⚠️  Warning: Found potential errors in indexer output${NC}"
        echo "First 20 lines of output:"
        head -20 indexer_output.log
    else
        echo -e "${GREEN}✅ No obvious errors detected${NC}"
    fi
    
    # Wait for the timeout to complete
    wait $INDEXER_PID 2>/dev/null
else
    echo -e "${RED}❌ Indexer stopped unexpectedly${NC}"
    echo "Last 20 lines of output:"
    tail -20 indexer_output.log
fi

echo -e "\n${GREEN}===================================${NC}"
echo -e "${GREEN}Indexer Test Complete${NC}"
echo -e "${GREEN}===================================${NC}"

echo -e "\n${YELLOW}Output saved to: apps/indexer/indexer_output.log${NC}"
echo -e "${YELLOW}To run the indexer continuously:${NC}"
echo -e "${YELLOW}  cd apps/indexer && pnpm start${NC}"