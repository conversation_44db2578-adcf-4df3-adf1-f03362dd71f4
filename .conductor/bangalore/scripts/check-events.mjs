#!/usr/bin/env node

import { SuiClient } from '@mysten/sui/client';

const client = new SuiClient({ url: 'https://fullnode.devnet.sui.io' });

// The package ID for your deployed HopFun contract
const HOPFUN_PACKAGE_ID = '0x06629ff9f0209763ca5e2a4c5fb1981577c76cffca1718e5a728929fd8dce940';

async function checkEvents() {
  try {
    console.log('Checking for BondingCurveCreated events...');
    console.log('Package ID:', HOPFUN_PACKAGE_ID);
    console.log('---');

    // Query for BondingCurveCreated events
    const events = await client.queryEvents({
      query: {
        MoveModule: {
          package: HOPFUN_PACKAGE_ID,
          module: 'events',
        },
      },
      limit: 50,
      order: 'descending',
    });

    console.log(`Found ${events.data.length} events from the package`);
    console.log('---');

    // Filter for BondingCurveCreated events
    const bondingCurveEvents = events.data.filter(event => 
      event.type.includes('BondingCurveCreated')
    );

    if (bondingCurveEvents.length > 0) {
      console.log(`Found ${bondingCurveEvents.length} BondingCurveCreated events:`);
      bondingCurveEvents.forEach((event, index) => {
        console.log(`\nEvent ${index + 1}:`);
        console.log('- Transaction:', event.id.txDigest);
        console.log('- Type:', event.type);
        console.log('- Timestamp:', new Date(Number(event.timestampMs)).toISOString());
        if (event.parsedJson) {
          console.log('- Data:', JSON.stringify(event.parsedJson, null, 2));
        }
      });
    } else {
      console.log('No BondingCurveCreated events found');
    }

    // Also check for ConnectorCreated events
    const connectorEvents = events.data.filter(event => 
      event.type.includes('ConnectorCreated')
    );

    if (connectorEvents.length > 0) {
      console.log(`\nFound ${connectorEvents.length} ConnectorCreated events:`);
      connectorEvents.forEach((event, index) => {
        console.log(`\nEvent ${index + 1}:`);
        console.log('- Transaction:', event.id.txDigest);
        console.log('- Type:', event.type);
        console.log('- Timestamp:', new Date(Number(event.timestampMs)).toISOString());
        if (event.parsedJson) {
          console.log('- Data:', JSON.stringify(event.parsedJson, null, 2));
        }
      });
    }

  } catch (error) {
    console.error('Error querying events:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

checkEvents();