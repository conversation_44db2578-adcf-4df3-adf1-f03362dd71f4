#!/bin/bash

# Simple MongoDB Replica Set Initialization Script

set -e

# Configuration
MONGO_HOST="${MONGO_HOST:-mongodb}"
MONGO_USERNAME="${MONGO_ROOT_USERNAME:-admin}"
MONGO_PASSWORD="${MONGO_ROOT_PASSWORD:-password}"
REPLICA_SET="${MONGO_REPLICA_SET_NAME:-rs0}"

echo "Waiting for MongoDB to be ready..."
sleep 10

# Try to initialize replica set
echo "Attempting to initialize replica set..."
mongosh --host ${MONGO_HOST}:27017 \
    --username ${MONGO_USERNAME} \
    --password ${MONGO_PASSWORD} \
    --authenticationDatabase admin \
    --eval "
    try {
        var status = rs.status();
        print('Replica set already initialized');
    } catch(e) {
        print('Initializing replica set...');
        rs.initiate({
            _id: '${REPLICA_SET}',
            members: [
                { _id: 0, host: '${MONGO_HOST}:27017' }
            ]
        });
        print('Replica set initialized');
    }
    " || true

echo "Waiting for replica set to be ready..."
sleep 5

echo "MongoDB replica set setup completed!"