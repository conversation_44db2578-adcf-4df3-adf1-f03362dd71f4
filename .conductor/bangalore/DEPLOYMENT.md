# HopFun Deployment Guide

This guide provides step-by-step instructions for deploying HopFun smart contracts to Sui blockchain and integrating them with the frontend application.

## Table of Contents
1. [Prerequisites](#prerequisites)
2. [Essential Scripts Overview](#essential-scripts-overview)
3. [Step 1: Environment Setup](#step-1-environment-setup)
4. [Step 2: Deploy Smart Contracts](#step-2-deploy-smart-contracts)
5. [Step 3: Verify Deployment](#step-3-verify-deployment)
6. [Step 4: Generate Frontend Bytecode](#step-4-generate-frontend-bytecode)
7. [Step 5: Frontend Integration](#step-5-frontend-integration)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

Before starting the deployment process, ensure you have:

- **Sui CLI** installed and configured
- **Node.js** v18+ and **pnpm** installed
- **MongoDB** with replica set support (for local development)
- **Sui wallet** with sufficient balance for gas fees
- Access to Sui devnet/testnet/mainnet

## Essential Scripts Overview

| Script | Purpose |
|--------|---------|
| `deploy-all-contracts.sh` | Deploys all smart contracts (Registry, HopFun, HopDex) to Sui |
| `check-deployment.sh` | Verifies deployment status and contract addresses |
| `extract-bytecode.js` | Generates bytecode for coin template used by frontend |
| `mongo-init.js` | Initializes MongoDB with required collections |
| `mongodb-keyfile` | MongoDB replica set authentication key |
| `rs-init.sh` | Initializes MongoDB replica set |
| `test-indexer.sh` | Tests the blockchain event indexer |

## Step 1: Environment Setup

### 1.1 Configure Sui CLI

```bash
# Switch to desired network
sui client switch --env devnet  # or testnet/mainnet

# Check your wallet address and balance
sui client active-address
sui client gas

# Get test tokens if on devnet/testnet
sui client faucet
```

### 1.2 Start MongoDB (for development)

```bash
# Start MongoDB with replica set using Docker
docker-compose up -d

# Verify MongoDB is running
docker-compose logs mongodb
```

### 1.3 Clone and Install Dependencies

```bash
# Install dependencies
pnpm install

# Build the database package
cd packages/database && pnpm build
cd ../..
```

## Step 2: Deploy Smart Contracts

The deployment process involves three main contracts that must be deployed in sequence:

1. **ConfigRegistry** - Central configuration storage
2. **HopDex** - DEX functionality for liquidity pools
3. **HopFun** - Core bonding curve functionality

### 2.1 Run the Deployment Script

```bash
# Deploy all contracts
bash scripts/deploy-all-contracts.sh
```

This script will:
- Check your SUI balance
- Deploy ConfigRegistry first
- Deploy HopDex with Registry dependency
- Deploy HopFun with Registry and HopDex dependencies
- Update `config/deployments.json` with deployed addresses
- Initialize the ConfigRegistry with all contract addresses

### 2.2 Expected Output

Successful deployment shows:
```
✅ All contracts deployed successfully!
   Registry Package: 0x9a52...
   HopFun Package:   0x497f...
   HopDex Package:   0x5055...
```

The script automatically updates `config/deployments.json`:
```json
{
  "deployments": {
    "devnet": {
      "registry": {
        "packageId": "0x9a52...",
        "registryId": "0x5e09...",
        "adminCapId": "0x44b6..."
      },
      "hopfun": {
        "packageId": "0x497f...",
        "memeConfigId": "0x864b...",
        "adminCapId": "0xb904..."
      },
      "hopdex": {
        "packageId": "0x5055...",
        "dexConfigId": "0x1123...",
        "adminCapId": "0x328c..."
      }
    }
  }
}
```

## Step 3: Verify Deployment

### 3.1 Check Deployment Status

```bash
# Run deployment verification
bash scripts/check-deployment.sh
```

This shows:
- Current network and wallet balance
- Deployment status for each contract
- On-chain verification of package existence
- Registry initialization status

### 3.2 Expected Verification Output

```
╔══════════════════════════════════════════════════════════════╗
║        Sui Contract Deployment Status (with Verification)    ║
╚══════════════════════════════════════════════════════════════╝

Network: devnet (Currently connected)
  Registry:
    Package ID: 0x9a52...
    Status:     ✅ Exists on-chain
    Registry ID: 0x5e09...
                ✅ Exists
    
  HopFun:
    Package ID: 0x497f...
    Status:     ✅ Exists on-chain
    MemeConfig: 0x864b...
                ✅ Exists

  HopDex:
    Package ID: 0x5055...
    Status:     ✅ Exists on-chain
    DexConfig:  0x1123...
                ✅ Exists

Status Summary:
  ✅ All packages are deployed and verified on devnet
```

## Step 4: Generate Frontend Bytecode

The frontend needs compiled bytecode of the coin template to create new tokens.

### 4.1 Extract Bytecode

```bash
# Generate bytecode for coin_template
node scripts/extract-bytecode.js
```

This script:
- Builds the `coin_template` Move module
- Extracts the compiled bytecode
- Updates dependencies with deployed contract addresses
- Saves to `apps/frontend/src/services/bytecode-data.json`

### 4.2 Verify Bytecode Generation

Check that `bytecode-data.json` contains:
```json
{
  "bytecode": "oRzrCwYAAAA...",
  "dependencies": [
    "0x1",                    // Sui stdlib
    "0x2",                    // Sui framework
    "0x9a52...",             // Registry package
    "0x5055...",             // HopDex package
    "0x497f..."              // HopFun package
  ],
  "generatedAt": "2025-01-13T03:28:10.980Z"
}
```

## Step 5: Frontend Integration

### 5.1 Frontend Configuration

Create/update `apps/frontend/.env.local`:
```bash
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_NETWORK=devnet  # or testnet/mainnet
```

### 5.2 Start the Frontend

```bash
# Start the frontend development server
pnpm run dev:frontend
```

### 5.3 Token Creation Flow

The frontend token creation process:

1. **User fills token details** → Launch Token Dialog
2. **First Transaction: Publish Coin Module**
   - Uses bytecode from `bytecode-data.json`
   - Creates CurrencyHolder and CoinMetadata
   - Returns package ID of new token
3. **Second Transaction: Create Connector**
   - Uses CurrencyHolder from first transaction
   - Registers with ConfigRegistry
   - Creates Connector object
4. **Third Transaction: Create Bonding Curve**
   - Uses Connector from second transaction
   - Initializes bonding curve parameters
   - Token is ready for trading

### 5.4 Verify Frontend Integration

1. Open the frontend at `http://localhost:3001`
2. Connect your Sui wallet
3. Click "Launch Token"
4. Fill in token details
5. Approve all three transactions

## Troubleshooting

### Common Issues and Solutions

#### 1. "PublishUpgradeMissingDependency" Error
**Cause:** Bytecode has incorrect dependencies
**Solution:** Run `node scripts/extract-bytecode.js` to regenerate

#### 2. "Object is not a package" Error
**Cause:** Package ID extraction issue
**Solution:** Already fixed in `token-creation.service.ts` - extracts from `objectChanges`

#### 3. "MoveAbort(..., 100)" Error
**Cause:** ConfigRegistry not initialized
**Solution:** Registry is auto-initialized during deployment

#### 4. "ZKLogin max epoch too large" Error
**Cause:** Outdated wallet cache
**Solution:** Clear wallet cache and reconnect

#### 5. Insufficient Gas Error
**Cause:** Low SUI balance
**Solution:** Run `sui client faucet` for devnet/testnet

### Verification Commands

```bash
# Check wallet balance
sui client gas

# Verify specific package exists
sui client object <PACKAGE_ID>

# Check registry configuration
sui client object <REGISTRY_ID> --json | jq '.content.fields'

# View recent transactions
sui client txns --limit 5
```

## Advanced Operations

### Manual Registry Update

If you need to update registry addresses manually:

```bash
sui client call \
  --package <REGISTRY_PACKAGE_ID> \
  --module registry \
  --function set_meme_config_address \
  --args <REGISTRY_ID> <ADMIN_CAP_ID> <NEW_MEME_CONFIG_ADDRESS>
```

### Redeployment

To redeploy contracts (e.g., after contract updates):

1. Update Move source code in `contracts/`
2. Run `bash scripts/deploy-all-contracts.sh`
3. Run `node scripts/extract-bytecode.js`
4. Restart frontend to use new addresses

### Network Migration

To deploy to a different network:

1. Switch Sui CLI: `sui client switch --env testnet`
2. Ensure sufficient balance: `sui client faucet`
3. Run deployment: `bash scripts/deploy-all-contracts.sh`
4. Update frontend `.env.local`: `NEXT_PUBLIC_NETWORK=testnet`

## Script Maintenance

### Adding New Scripts

Place new scripts in the `scripts/` directory and update this documentation.

### Script Dependencies

- All scripts assume working directory is project root
- Scripts use `jq` for JSON processing
- Bash scripts tested on macOS/Linux

## Support

For issues or questions:
- Check deployment status: `bash scripts/check-deployment.sh`
- Verify bytecode: `cat apps/frontend/src/services/bytecode-data.json | jq`
- Review logs: `docker-compose logs mongodb` (for database issues)

---

**Last Updated:** January 2025
**Tested On:** Sui Devnet v1.53.2