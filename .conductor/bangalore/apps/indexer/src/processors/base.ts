import type { PrismaClient } from '@hopfun/database';
import { createPrismaClient } from '@hopfun/database';
import { logger } from '@hopfun/logger';
import type { SuiEvent } from '@mysten/sui/client';
import { EventEmitter } from 'events';
import { ObjectId } from 'mongodb';

import { env, Network } from '@/config/environment';

export interface ProcessingResult {
  success: boolean;
  error?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data?: any;
  shouldRetry?: boolean;
}

export interface ProcessingContext {
  event: SuiEvent;
  network: Network;
  retryCount: number;
  transactionDigest?: string;
  blockHeight?: string;
}

export abstract class BaseEventProcessor extends EventEmitter {
  protected db: PrismaClient;
  protected network: Network;
  protected readonly maxRetries: number;

  constructor(network: Network = Network.TESTNET) {
    super();
    this.network = network;
    this.maxRetries = env.MAX_RETRIES;
    this.db = createPrismaClient(env.DATABASE_URL);
  }

  abstract getEventType(): string;
  abstract process(context: ProcessingContext): Promise<ProcessingResult>;

  async processEvent(
    event: SuiEvent,
    retryCount = 0,
  ): Promise<ProcessingResult> {
    logger.debug(
      {
        eventType: this.getEventType(),
        eventId: event.id,
        retryCount,
      },
      'Base processEvent called',
    );

    const context: ProcessingContext = {
      event,
      network: this.network,
      retryCount,
      transactionDigest: event.id.txDigest,
      blockHeight: undefined, // Will be filled from transaction details if needed
    };

    const startTime = Date.now();

    try {
      logger.debug(
        {
          eventType: this.getEventType(),
          eventId: event.id,
          transactionDigest: event.id.txDigest,
          retryCount,
          network: this.network,
        },
        'Processing event',
      );

      // Validate event structure
      logger.debug('Base: Validating event structure');
      const isValid = this.validateEvent(event);
      logger.debug(`Base: Event validation result: ${isValid}`);

      if (!isValid) {
        const error = 'Invalid event structure';
        logger.debug('Base: Event validation failed');
        logger.warn(
          {
            eventType: this.getEventType(),
            eventId: event.id,
            event: event.parsedJson,
          },
          error,
        );

        return {
          success: false,
          error,
          shouldRetry: false,
        };
      }

      // Check if event already processed
      logger.debug('Base: Checking if event already processed');
      const alreadyProcessed = await this.isEventProcessed(event);
      logger.debug(`Base: Already processed check result: ${alreadyProcessed}`);

      if (alreadyProcessed) {
        logger.debug('Base: Event already processed, skipping');
        logger.debug(
          {
            eventType: this.getEventType(),
            eventId: event.id,
          },
          'Event already processed, skipping',
        );

        return {
          success: true,
          data: { skipped: true, reason: 'already_processed' },
        };
      }

      // Process the event
      logger.debug('Base: Calling process method');
      const result = await this.process(context);
      logger.debug('Base: Process method returned:', result);

      const processingTime = Date.now() - startTime;

      if (result.success) {
        logger.info(
          {
            eventType: this.getEventType(),
            eventId: event.id,
            processingTimeMs: processingTime,
            network: this.network,
          },
          'Event processed successfully',
        );

        this.emit('eventProcessed', {
          eventType: this.getEventType(),
          eventId: event.id,
          processingTime,
          result,
        });
      } else {
        logger.error(
          {
            eventType: this.getEventType(),
            eventId: event.id,
            error: result.error,
            retryCount,
            processingTimeMs: processingTime,
            network: this.network,
          },
          'Event processing failed',
        );

        this.emit('eventFailed', {
          eventType: this.getEventType(),
          eventId: event.id,
          error: result.error,
          retryCount,
          result,
        });
      }

      return result;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      logger.error(
        {
          eventType: this.getEventType(),
          eventId: event.id,
          error: errorMessage,
          retryCount,
          processingTimeMs: processingTime,
          network: this.network,
          stack: error instanceof Error ? error.stack : undefined,
        },
        'Event processing error',
      );

      this.emit('eventError', {
        eventType: this.getEventType(),
        eventId: event.id,
        error,
        retryCount,
      });

      return {
        success: false,
        error: errorMessage,
        shouldRetry: this.shouldRetryError(error),
      };
    }
  }

  protected abstract validateEvent(event: SuiEvent): boolean;

  protected async isEventProcessed(event: SuiEvent): Promise<boolean> {
    try {
      logger.debug(
        {
          eventSeq: event.id.eventSeq,
          txDigest: event.id.txDigest,
          network: this.network,
        },
        'Base: isEventProcessed - checking queue entry for',
      );

      // Check in the processing queue first
      const queueEntry = await this.db.processingQueue.findFirst({
        where: {
          eventData: {
            equals: {
              id: {
                eventSeq: event.id.eventSeq,
                txDigest: event.id.txDigest,
              },
            },
          },
          network: this.network,
          processed: true,
        },
      });

      logger.debug(
        {
          found: !!queueEntry,
          queueEntry: queueEntry
            ? {
                id: queueEntry.id,
                processed: queueEntry.processed,
                eventType: queueEntry.eventType,
              }
            : null,
        },
        'Base: isEventProcessed - queue entry result',
      );

      if (queueEntry) {
        logger.debug('Base: isEventProcessed - found in queue, returning true');
        return true;
      }

      // Check event-specific tables based on event type
      logger.debug(
        'Base: isEventProcessed - checking event-specific processing',
      );
      const eventSpecificResult =
        await this.checkEventSpecificProcessing(event);
      logger.debug(
        `Base: isEventProcessed - event-specific result: ${eventSpecificResult}`,
      );

      return eventSpecificResult;
    } catch (error) {
      logger.debug(error, 'Base: isEventProcessed - error occurred');
      logger.error(
        {
          eventId: event.id,
          error: error instanceof Error ? error.message : String(error),
        },
        'Error checking if event is processed',
      );

      // If we can't determine, assume not processed to avoid data loss
      return false;
    }
  }

  protected abstract checkEventSpecificProcessing(
    event: SuiEvent,
  ): Promise<boolean>;

  protected shouldRetryError(error: unknown): boolean {
    if (error instanceof Error) {
      // Don't retry on validation errors
      if (
        error.message.includes('validation') ||
        error.message.includes('invalid')
      ) {
        return false;
      }

      // Don't retry on duplicate key errors
      if (
        error.message.includes('duplicate') ||
        error.message.includes('unique constraint')
      ) {
        return false;
      }

      // Retry on network/database connection errors
      if (
        error.message.includes('network') ||
        error.message.includes('connection') ||
        error.message.includes('timeout')
      ) {
        return true;
      }
    }

    // Default to retry for unknown errors
    return true;
  }

  protected async addToProcessingQueue(
    event: SuiEvent,
    error?: string,
  ): Promise<void> {
    try {
      await this.db.processingQueue.create({
        data: {
          id: new ObjectId().toString(),
          network: this.network,
          eventType: this.getEventType(),
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          eventData: event as any,
          lastError: error,
          processed: false,
        },
      });
    } catch (err) {
      logger.error(
        {
          eventId: event.id,
          error: err instanceof Error ? err.message : String(err),
        },
        'Failed to add event to processing queue',
      );
    }
  }

  protected async markEventProcessed(event: SuiEvent): Promise<void> {
    try {
      await this.db.processingQueue.updateMany({
        where: {
          eventData: {
            equals: {
              id: {
                eventSeq: event.id.eventSeq,
                txDigest: event.id.txDigest,
              },
            },
          },
          network: this.network,
        },
        data: {
          processed: true,
          processedAt: new Date(),
        },
      });
    } catch (error) {
      logger.error(
        {
          eventId: event.id,
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to mark event as processed',
      );
    }
  }

  // Utility methods for common operations
  protected calculatePriceImpact(prePrice: string, postPrice: string): string {
    try {
      const pre = BigInt(prePrice);
      const post = BigInt(postPrice);

      if (pre === BigInt(0)) {
        return '0';
      }

      const impact = ((post - pre) * BigInt(10000)) / pre;
      return impact.toString();
    } catch (error) {
      logger.warn(
        {
          prePrice,
          postPrice,
          error: error instanceof Error ? error.message : String(error),
        },
        'Error calculating price impact',
      );
      return '0';
    }
  }

  protected async withTransaction<T>(
    operation: (
      tx: Omit<
        PrismaClient,
        | '$connect'
        | '$disconnect'
        | '$on'
        | '$transaction'
        | '$use'
        | '$extends'
      >,
    ) => Promise<T>,
  ): Promise<T> {
    return await this.db.$transaction(operation);
  }

  async cleanup(): Promise<void> {
    try {
      await this.db.$disconnect();
      this.removeAllListeners();

      logger.info(
        {
          eventType: this.getEventType(),
          network: this.network,
        },
        'Event processor cleaned up',
      );
    } catch (error) {
      logger.error(
        {
          eventType: this.getEventType(),
          error: error instanceof Error ? error.message : String(error),
        },
        'Error during cleanup',
      );
    }
  }
}

export default BaseEventProcessor;
