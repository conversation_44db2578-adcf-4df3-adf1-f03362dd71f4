import { logger } from '@hopfun/logger';
import type { SuiEvent } from '@mysten/sui/client';
import { EventEmitter } from 'events';
import PQueue from 'p-queue';
import pRetry from 'p-retry';

import type { Network } from '@/config/environment';
import { env } from '@/config/environment';
import type { HopfunEventType } from '@/sui/events';
import { getEventType } from '@/sui/events';

import type { BaseEventProcessor, ProcessingResult } from './base';

export interface ProcessingMetrics {
  totalProcessed: number;
  totalFailed: number;
  totalRetries: number;
  processingRate: number; // events per minute
  averageProcessingTime: number;
  errorRate: number;
}

export interface ProcessorConfig {
  network: Network;
  concurrency?: number;
  retryOptions?: {
    retries?: number;
    factor?: number;
    minTimeout?: number;
    maxTimeout?: number;
  };
}

export class EventProcessingEngine extends EventEmitter {
  private processors = new Map<HopfunEventType, BaseEventProcessor>();
  private processingQueue: PQueue;
  private network: Network;
  private metrics: ProcessingMetrics;
  private metricsInterval?: NodeJS.Timeout;
  private isRunning = false;

  constructor(config: ProcessorConfig) {
    super();
    this.network = config.network;

    // Initialize processing queue with concurrency control
    this.processingQueue = new PQueue({
      concurrency: config.concurrency ?? 5,
      interval: 1000, // Process at most N events per second
      intervalCap: 50,
    });

    // Initialize metrics
    this.metrics = {
      totalProcessed: 0,
      totalFailed: 0,
      totalRetries: 0,
      processingRate: 0,
      averageProcessingTime: 0,
      errorRate: 0,
    };

    this.setupEventHandlers();

    logger.info(
      {
        network: this.network,
        concurrency: config.concurrency ?? 5,
      },
      'Event processing engine initialized',
    );
  }

  registerProcessor(
    eventType: HopfunEventType,
    processor: BaseEventProcessor,
  ): void {
    this.processors.set(eventType, processor);

    // Forward processor events
    processor.on('eventProcessed', (data) => {
      this.updateMetrics('processed', data.processingTime);
      this.emit('eventProcessed', data);
    });

    processor.on('eventFailed', (data) => {
      this.updateMetrics('failed');
      this.emit('eventFailed', data);
    });

    processor.on('eventError', (data) => {
      this.updateMetrics('error');
      this.emit('eventError', data);
    });

    logger.info(
      {
        eventType,
        network: this.network,
      },
      'Registered event processor',
    );
  }

  unregisterProcessor(eventType: HopfunEventType): void {
    const processor = this.processors.get(eventType);
    if (processor) {
      processor.removeAllListeners();
      this.processors.delete(eventType);

      logger.info(
        {
          eventType,
          network: this.network,
        },
        'Unregistered event processor',
      );
    }
  }

  async processEvent(event: SuiEvent): Promise<ProcessingResult> {
    logger.debug(
      {
        eventId: event.id,
        eventType: event.type,
        isRunning: this.isRunning,
      },
      'Engine processEvent called',
    );

    if (!this.isRunning) {
      throw new Error('Event processing engine is not running');
    }

    const eventType = getEventType(event.type);
    logger.debug(
      {
        eventId: event.id,
        originalType: event.type,
        mappedType: eventType,
      },
      'Engine getEventType result',
    );

    if (!eventType) {
      logger.debug('Engine: Unknown event type, skipping');
      logger.debug(
        {
          eventType: event.type,
          eventId: event.id,
        },
        'Unknown event type, skipping',
      );

      return {
        success: true,
        data: { skipped: true, reason: 'unknown_event_type' },
      };
    }

    const processor = this.processors.get(eventType);
    logger.debug(
      {
        eventId: event.id,
        eventType,
        hasProcessor: !!processor,
        availableProcessors: Array.from(this.processors.keys()),
      },
      'Engine processor lookup',
    );

    if (!processor) {
      logger.debug('Engine: No processor found for event type');
      logger.warn(
        {
          eventType,
          eventId: event.id,
        },
        'No processor registered for event type',
      );

      return {
        success: false,
        error: `No processor registered for event type: ${eventType}`,
        shouldRetry: false,
      };
    }

    // Add to processing queue with retry logic
    return new Promise((resolve, reject) => {
      this.processingQueue
        .add(async () => {
          try {
            const result = await pRetry(
              async (attemptNumber) => {
                const processingResult = await processor.processEvent(
                  event,
                  attemptNumber - 1,
                );

                if (!processingResult.success && processingResult.shouldRetry) {
                  this.updateMetrics('retry');
                  throw new Error(
                    processingResult.error ?? 'Processing failed',
                  );
                }

                return processingResult;
              },
              {
                retries: env.MAX_RETRIES,
                factor: 2,
                minTimeout: 1000,
                maxTimeout: 10000,
                onFailedAttempt: (error) => {
                  logger.warn(
                    {
                      eventType,
                      eventId: event.id,
                      attempt: error.attemptNumber,
                      retriesLeft: error.retriesLeft,
                      error: error.message,
                    },
                    'Event processing attempt failed',
                  );
                },
              },
            );

            resolve(result);
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : String(error);

            logger.error(
              {
                eventType,
                eventId: event.id,
                error: errorMessage,
              },
              'Event processing failed after all retries',
            );

            const failedResult: ProcessingResult = {
              success: false,
              error: errorMessage,
              shouldRetry: false,
            };

            resolve(failedResult);
          }
        })
        .catch(reject);
    });
  }

  async processEvents(events: SuiEvent[]): Promise<ProcessingResult[]> {
    if (!this.isRunning) {
      throw new Error('Event processing engine is not running');
    }

    logger.info(
      {
        count: events.length,
        network: this.network,
      },
      'Processing batch of events',
    );

    const results: ProcessingResult[] = [];

    // Process events in batches to avoid overwhelming the system
    const batchSize = 10;

    for (let i = 0; i < events.length; i += batchSize) {
      const batch = events.slice(i, i + batchSize);

      const batchPromises = batch.map((event) => this.processEvent(event));
      const batchResults = await Promise.allSettled(batchPromises);

      for (const result of batchResults) {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          logger.error(
            {
              error: result.reason,
              network: this.network,
            },
            'Batch processing error',
          );

          results.push({
            success: false,
            error: result.reason?.message ?? 'Unknown error',
            shouldRetry: true,
          });
        }
      }

      // Small delay between batches to prevent overwhelming downstream systems
      if (i + batchSize < events.length) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    }

    logger.info(
      {
        total: events.length,
        successful: results.filter((r) => r.success).length,
        failed: results.filter((r) => !r.success).length,
        network: this.network,
      },
      'Finished processing event batch',
    );

    return results;
  }

  start(): void {
    if (this.isRunning) {
      logger.warn({}, 'Event processing engine already running');
      return;
    }

    this.isRunning = true;

    // Start metrics collection
    this.startMetricsCollection();

    logger.info(
      {
        network: this.network,
        registeredProcessors: Array.from(this.processors.keys()),
      },
      'Event processing engine started',
    );

    this.emit('started');
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      logger.warn({}, 'Event processing engine already stopped');
      return;
    }

    this.isRunning = false;

    logger.info(
      {
        network: this.network,
        queueSize: this.processingQueue.size,
        pending: this.processingQueue.pending,
      },
      'Stopping event processing engine',
    );

    // Wait for current events to finish processing
    await this.processingQueue.onIdle();

    // Stop metrics collection
    this.stopMetricsCollection();

    // Cleanup processors
    for (const processor of this.processors.values()) {
      await processor.cleanup();
    }

    logger.info(
      {
        network: this.network,
      },
      'Event processing engine stopped',
    );

    this.emit('stopped');
  }

  getMetrics(): ProcessingMetrics {
    return { ...this.metrics };
  }

  getQueueStatus() {
    return {
      size: this.processingQueue.size,
      pending: this.processingQueue.pending,
      isPaused: this.processingQueue.isPaused,
    };
  }

  pauseProcessing(): void {
    this.processingQueue.pause();
    logger.info({ network: this.network }, 'Event processing paused');
    this.emit('paused');
  }

  resumeProcessing(): void {
    this.processingQueue.start();
    logger.info({ network: this.network }, 'Event processing resumed');
    this.emit('resumed');
  }

  private setupEventHandlers(): void {
    this.processingQueue.on('active', () => {
      this.emit('queueActive');
    });

    this.processingQueue.on('idle', () => {
      this.emit('queueIdle');
    });

    this.processingQueue.on('add', () => {
      this.emit('queueAdd');
    });

    this.processingQueue.on('next', () => {
      this.emit('queueNext');
    });
  }

  private updateMetrics(
    type: 'processed' | 'failed' | 'error' | 'retry',
    processingTime?: number,
  ): void {
    switch (type) {
      case 'processed':
        this.metrics.totalProcessed++;
        if (processingTime) {
          this.metrics.averageProcessingTime =
            (this.metrics.averageProcessingTime + processingTime) / 2;
        }
        break;
      case 'failed':
        this.metrics.totalFailed++;
        break;
      case 'error':
        // Error rate calculation handled in metrics collection
        break;
      case 'retry':
        this.metrics.totalRetries++;
        break;
    }
  }

  private startMetricsCollection(): void {
    let lastProcessedCount = 0;
    let lastTimestamp = Date.now();

    this.metricsInterval = setInterval(() => {
      const currentTime = Date.now();
      const timeDiff = (currentTime - lastTimestamp) / 1000 / 60; // minutes
      const processedDiff = this.metrics.totalProcessed - lastProcessedCount;

      this.metrics.processingRate = timeDiff > 0 ? processedDiff / timeDiff : 0;

      const totalEvents =
        this.metrics.totalProcessed + this.metrics.totalFailed;
      this.metrics.errorRate =
        totalEvents > 0 ? this.metrics.totalFailed / totalEvents : 0;

      lastProcessedCount = this.metrics.totalProcessed;
      lastTimestamp = currentTime;

      this.emit('metricsUpdated', this.metrics);
    }, 60000); // Update every minute
  }

  private stopMetricsCollection(): void {
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
      this.metricsInterval = undefined;
    }
  }
}

export default EventProcessingEngine;
