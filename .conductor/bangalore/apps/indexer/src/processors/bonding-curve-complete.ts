import type { PrismaClient } from '@hopfun/database';
import { TokenStatus } from '@hopfun/database';
import { logger } from '@hopfun/logger';
import type { SuiEvent } from '@mysten/sui/client';
import { ObjectId } from 'mongodb';

import type { Network } from '@/config/environment';
import { EventValidator } from '@/sui/events';

import type { ProcessingContext, ProcessingResult } from './base';
import { BaseEventProcessor } from './base';

export class BondingCurveCompleteProcessor extends BaseEventProcessor {
  constructor(network: Network) {
    super(network);
  }

  getEventType(): string {
    return 'BondingCurveComplete';
  }

  protected validateEvent(event: SuiEvent): boolean {
    return (
      event.parsedJson != null &&
      typeof event.parsedJson === 'object' &&
      EventValidator.isValidBondingCurveCompleteEvent(
        event.parsedJson as Record<string, unknown>,
      )
    );
  }

  protected async checkEventSpecificProcessing(
    event: SuiEvent,
  ): Promise<boolean> {
    try {
      const existingEvent = await this.db.bondingCurveCompleteEvent.findUnique({
        where: {
          transactionId: event.id.txDigest,
        },
      });

      return !!existingEvent;
    } catch (error) {
      logger.error(
        {
          eventId: event.id,
          error: error instanceof Error ? error.message : String(error),
        },
        'Error checking bonding curve complete event processing',
      );
      return false;
    }
  }

  async process(context: ProcessingContext): Promise<ProcessingResult> {
    const { event, network } = context;

    if (
      !event.parsedJson ||
      !EventValidator.isValidBondingCurveCompleteEvent(event.parsedJson)
    ) {
      return {
        success: false,
        error: 'Invalid BondingCurveComplete event data',
        shouldRetry: false,
      };
    }

    const eventData = event.parsedJson;

    try {
      await this.withTransaction(async (tx) => {
        // Store the raw bonding curve complete event
        await tx.bondingCurveCompleteEvent.create({
          data: {
            id: new ObjectId().toString(),
            curveId: eventData.curve_id,
            transactionId: event.id.txDigest,
            createdAt: new Date(Number(event.timestampMs)),
            package: event.packageId,
            module: event.type.split('::')[1] || 'events',
            eventType: this.getEventType(),
            network,
          },
        });

        // Find and update the token status
        const token = await tx.token.findUnique({
          where: { curveId: eventData.curve_id },
        });

        if (!token) {
          throw new Error(
            `Token not found for curve ID: ${eventData.curve_id}`,
          );
        }

        // Update token status to COMPLETED
        await tx.token.update({
          where: { id: token.id },
          data: {
            status: TokenStatus.COMPLETED,
            completedAt: new Date(Number(event.timestampMs)),
            updatedAt: new Date(Number(event.timestampMs)),
          },
        });

        // Calculate final market cap based on total supply and final price
        const finalMarketCap = this.calculateMarketCap(
          token.totalSupply,
          token.currentPrice,
        );

        // Update token stats with completion metrics
        await tx.tokenStats.updateMany({
          where: { curveId: eventData.curve_id },
          data: {
            lastUpdated: new Date(Number(event.timestampMs)),
          },
        });

        // Update user stats for all holders to reflect completion
        await this.updateHolderStatsOnCompletion(
          tx,
          token.id,
          eventData.curve_id,
        );

        logger.info(
          {
            curveId: eventData.curve_id,
            tokenId: token.id,
            ticker: token.ticker,
            finalMarketCap,
            transactionId: event.id.txDigest,
            network,
          },
          'Processed BondingCurveComplete event',
        );
      });

      // Mark as processed
      await this.markEventProcessed(event);

      return {
        success: true,
        data: {
          curveId: eventData.curve_id,
          status: 'COMPLETED',
          transactionId: event.id.txDigest,
        },
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      logger.error(
        {
          curveId: eventData.curve_id,
          transactionId: event.id.txDigest,
          error: errorMessage,
          network,
        },
        'Failed to process BondingCurveComplete event',
      );

      // Add to processing queue for retry
      await this.addToProcessingQueue(event, errorMessage);

      return {
        success: false,
        error: errorMessage,
        shouldRetry: this.shouldRetryError(error),
      };
    }
  }

  private calculateMarketCap(
    totalSupply: string,
    currentPrice: string,
  ): string {
    try {
      const supply = BigInt(totalSupply);
      const price = BigInt(currentPrice);

      // Market cap = total supply * current price
      // Assuming price is already scaled appropriately
      const marketCap = (supply * price) / BigInt(1000000000); // Adjust for price scalar

      return marketCap.toString();
    } catch (error) {
      logger.warn(
        {
          totalSupply,
          currentPrice,
          error: error instanceof Error ? error.message : String(error),
        },
        'Error calculating market cap',
      );
      return '0';
    }
  }

  private async updateHolderStatsOnCompletion(
    tx: Omit<
      PrismaClient,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >,
    tokenId: string,
    curveId: string,
  ): Promise<void> {
    try {
      // Get all current holders
      const holders = await tx.userHolding.findMany({
        where: {
          tokenId,
          tokenAmount: {
            gt: '0',
          },
        },
      });

      // Update holder count in token stats
      const holderCount = holders.length;

      await tx.tokenStats.updateMany({
        where: { curveId },
        data: {
          totalHolders: holderCount,
        },
      });

      logger.info(
        {
          tokenId,
          curveId,
          totalHolders: holderCount,
        },
        'Updated holder stats on completion',
      );
    } catch (error) {
      logger.error(
        {
          tokenId,
          curveId,
          error: error instanceof Error ? error.message : String(error),
        },
        'Error updating holder stats on completion',
      );
    }
  }
}

export default BondingCurveCompleteProcessor;
