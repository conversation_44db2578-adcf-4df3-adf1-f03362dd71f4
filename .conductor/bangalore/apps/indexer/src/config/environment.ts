import { logger } from '@hopfun/logger';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { z } from 'zod';

// Get __dirname equivalent for ES modules
// @ts-expect-error - TypeScript does not recognize import.meta.url in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load .env file from the indexer app directory - try multiple locations
const envPaths = [
  path.resolve(process.cwd(), 'apps/indexer/.env'), // When running from root
  path.resolve(process.cwd(), '.env'), // When running from indexer directory
  path.resolve(__dirname, '../../.env'), // Relative to src/config/environment.ts -> apps/indexer/.env
];

let envLoaded = false;
for (const envPath of envPaths) {
  try {
    const result = dotenv.config({ path: envPath });
    if (result.parsed && Object.keys(result.parsed).length > 0) {
      envLoaded = true;
      logger.info(`Environment loaded from: ${envPath}`);
      logger.info(
        `DATABASE_URL: ${process.env.DATABASE_URL?.substring(0, 50)}...`,
      );
      break;
    }
  } catch (error) {
    // Continue to next path
    logger.warn({ error }, `Failed to load .env from ${envPath}`);
  }
}

if (!envLoaded) {
  logger.warn('No .env file found, using system environment variables');
}

export enum Network {
  MAINNET = 'MAINNET',
  DEVNET = 'DEVNET',
  TESTNET = 'TESTNET',
}

const EnvironmentSchema = z.object({
  NODE_ENV: z
    .enum(['development', 'production', 'test'])
    .default('development'),

  // Network Configuration
  NETWORK: z.enum(Network).default(Network.TESTNET),

  // Database
  DATABASE_URL: z.string().min(1, 'DATABASE_URL is required'),

  // Sui Network Configuration
  SUI_RPC_URL: z.url('Invalid SUI_RPC_URL'),
  SUI_WS_URL: z.url('Invalid SUI_WS_URL').optional(),

  // Contract Configuration
  HOPFUN_PACKAGE_ID: z.string().min(1, 'HOPFUN_PACKAGE_ID is required'),
  HOPDEX_PACKAGE_ID: z.string().min(1, 'HOPDEX_PACKAGE_ID is required'),

  // Indexer Configuration
  START_CHECKPOINT: z.coerce.number().optional(),
  BATCH_SIZE: z.coerce.number().default(100),
  PROCESSING_DELAY_MS: z.coerce.number().default(1000),
  MAX_RETRIES: z.coerce.number().default(3),

  // Performance Configuration
  ENABLE_METRICS: z.coerce.boolean().default(true),
  METRICS_PORT: z.coerce.number().default(9090),
  HEALTH_CHECK_PORT: z.coerce.number().default(3001),

  // Redis (optional for caching)
  REDIS_URL: z.string().optional(),

  // Logging
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),

  // WebSocket Configuration
  WS_RECONNECT_ATTEMPTS: z.coerce.number().default(5),
  WS_RECONNECT_DELAY_MS: z.coerce.number().default(5000),
});

export type Environment = z.infer<typeof EnvironmentSchema>;

class ConfigError extends Error {
  constructor(message: string) {
    super(`Configuration Error: ${message}`);
    this.name = 'ConfigError';
  }
}

function validateEnvironment(): Environment {
  try {
    return EnvironmentSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.issues.map(
        (err: z.core.$ZodIssue) => `${err.path.join('.')}: ${err.message}`,
      );
      throw new ConfigError(
        `Invalid environment configuration:\n${errorMessages.join('\n')}`,
      );
    }
    throw error;
  }
}

export const env = validateEnvironment();

// Network-specific configurations
export const NetworkConfig = {
  [Network.MAINNET]: {
    rpcUrl: env.SUI_RPC_URL,
    wsUrl: env.SUI_WS_URL,
    explorerUrl: 'https://suiscan.xyz/mainnet',
    packagesIds: {
      hopfun: env.HOPFUN_PACKAGE_ID,
      hopdex: env.HOPDEX_PACKAGE_ID,
    },
  },
  [Network.DEVNET]: {
    rpcUrl: env.SUI_RPC_URL,
    wsUrl: env.SUI_WS_URL,
    explorerUrl: 'https://suiscan.xyz/devnet',
    packagesIds: {
      hopfun: env.HOPFUN_PACKAGE_ID,
      hopdex: env.HOPDEX_PACKAGE_ID,
    },
  },
  [Network.TESTNET]: {
    rpcUrl: env.SUI_RPC_URL,
    wsUrl: env.SUI_WS_URL,
    explorerUrl: 'https://suiscan.xyz/testnet',
    packagesIds: {
      hopfun: env.HOPFUN_PACKAGE_ID,
      hopdex: env.HOPDEX_PACKAGE_ID,
    },
  },
} as const;

export const getNetworkConfig = (network: Network = env.NETWORK) => {
  return NetworkConfig[network];
};

// Validate network-specific configuration
export function validateNetworkConfig(network: Network): void {
  const config = getNetworkConfig(network);

  if (!config.rpcUrl) {
    throw new ConfigError(`RPC URL not configured for network: ${network}`);
  }

  if (!config.packagesIds.hopfun) {
    throw new ConfigError(
      `HOPFUN_PACKAGE_ID not configured for network: ${network}`,
    );
  }

  if (!config.packagesIds.hopdex) {
    throw new ConfigError(
      `HOPDEX_PACKAGE_ID not configured for network: ${network}`,
    );
  }
}

// Export commonly used configurations
export const isDevelopment = env.NODE_ENV === 'development';
export const isProduction = env.NODE_ENV === 'production';
export const isTest = env.NODE_ENV === 'test';

export default env;
