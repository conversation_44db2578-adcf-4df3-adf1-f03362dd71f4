#!/usr/bin/env node

const { PrismaClient } = require('@hopfun/database');
const { createSuiClient } = require('@mysten/sui/client');
const dotenv = require('dotenv');
const fs = require('fs');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env') });

console.log('=====================================');
console.log('Testing Indexer Functionality');
console.log('=====================================\n');

async function testIndexer() {
  // 1. Check environment variables
  console.log('1. Environment Configuration:');
  const requiredVars = [
    'DATABASE_URL',
    'SUI_RPC_URL',
    'HOPFUN_PACKAGE_ID',
    'HOPDEX_PACKAGE_ID',
    'NETWORK'
  ];

  let allVarsPresent = true;
  for (const varName of requiredVars) {
    const value = process.env[varName];
    if (value) {
      console.log(`✅ ${varName}: ${value.substring(0, 50)}...`);
    } else {
      console.log(`❌ ${varName}: NOT SET`);
      allVarsPresent = false;
    }
  }

  if (!allVarsPresent) {
    console.error('\n❌ Missing required environment variables');
    process.exit(1);
  }

  // 2. Test Database Connection
  console.log('\n2. Testing Database Connection:');
  const prisma = new PrismaClient();

  try {
    await prisma.$connect();
    console.log('✅ Successfully connected to MongoDB');
    
    // Check database state
    const [tokenCount, indexerState, eventCount, txCount] = await Promise.all([
      prisma.token.count(),
      prisma.indexerState.findFirst(),
      prisma.connectorCreatedEvent.count(),
      prisma.bondingCurveTransaction.count()
    ]);
    
    console.log(`   - Tokens in database: ${tokenCount}`);
    console.log(`   - Connector events: ${eventCount}`);
    console.log(`   - Transactions: ${txCount}`);
    console.log(`   - Indexer state: ${indexerState ? `Checkpoint ${indexerState.lastProcessedCheckpoint}` : 'Not initialized'}`);
    
    // Check for recent tokens
    const recentTokens = await prisma.token.findMany({
      take: 3,
      orderBy: { createdAt: 'desc' },
      include: { stats: true }
    });
    
    if (recentTokens.length > 0) {
      console.log('\n   Recent tokens:');
      for (const token of recentTokens) {
        console.log(`   - ${token.coinName} (${token.ticker})`);
        console.log(`     Market Cap: ${token.marketCap}`);
        console.log(`     Status: ${token.status}`);
        console.log(`     Created: ${token.createdAt}`);
        if (token.stats) {
          console.log(`     Volume 24h: ${token.stats.volume24h}`);
          console.log(`     Transactions 24h: ${token.stats.transactions24h}`);
        }
      }
    } else {
      console.log('   No tokens found in database yet');
    }
    
  } catch (error) {
    console.error(`❌ Database connection failed: ${error.message}`);
    await prisma.$disconnect();
    process.exit(1);
  }

  // 3. Test Sui RPC Connection
  console.log('\n3. Testing Sui RPC Connection:');
  try {
    const client = createSuiClient({
      url: process.env.SUI_RPC_URL
    });
    
    const latestCheckpoint = await client.getLatestCheckpointSequenceNumber();
    console.log(`✅ Successfully connected to Sui RPC`);
    console.log(`   Latest checkpoint: ${latestCheckpoint}`);
    
    // Check if our contracts are deployed
    console.log('\n4. Verifying Deployed Contracts:');
    
    const packages = [
      { name: 'HopFun', id: process.env.HOPFUN_PACKAGE_ID },
      { name: 'HopDex', id: process.env.HOPDEX_PACKAGE_ID }
    ];
    
    for (const pkg of packages) {
      try {
        const packageObj = await client.getObject({
          id: pkg.id,
          options: { showType: true }
        });
        
        if (packageObj.data) {
          console.log(`✅ ${pkg.name} package found: ${pkg.id}`);
        } else {
          console.log(`❌ ${pkg.name} package not found: ${pkg.id}`);
        }
      } catch (error) {
        console.log(`❌ Error checking ${pkg.name}: ${error.message}`);
      }
    }
    
    // Check for recent events
    console.log('\n5. Checking for Recent Events:');
    try {
      // Query recent events from HopFun package
      const events = await client.queryEvents({
        query: { 
          MoveModule: { 
            package: process.env.HOPFUN_PACKAGE_ID,
            module: 'connector'
          } 
        },
        limit: 5,
        order: 'descending'
      });
      
      if (events.data && events.data.length > 0) {
        console.log(`   Found ${events.data.length} recent connector events:`);
        for (const event of events.data) {
          console.log(`   - ${event.type}`);
          console.log(`     Checkpoint: ${event.checkpoint}`);
          console.log(`     Timestamp: ${event.timestampMs ? new Date(parseInt(event.timestampMs)).toISOString() : 'N/A'}`);
        }
      } else {
        console.log('   No recent events found from HopFun package');
      }
      
      // Try to get bonding curve events
      const bondingEvents = await client.queryEvents({
        query: { 
          MoveModule: { 
            package: process.env.HOPFUN_PACKAGE_ID,
            module: 'bonding_curve'
          } 
        },
        limit: 5,
        order: 'descending'
      });
      
      if (bondingEvents.data && bondingEvents.data.length > 0) {
        console.log(`\n   Found ${bondingEvents.data.length} recent bonding curve events`);
      }
      
    } catch (error) {
      console.log(`   Error querying events: ${error.message}`);
    }
    
  } catch (error) {
    console.error(`❌ Sui RPC connection failed: ${error.message}`);
    await prisma.$disconnect();
    process.exit(1);
  }

  // 6. Test if indexer can start
  console.log('\n6. Testing Indexer Module Import:');
  try {
    // Try to import the main indexer module
    const indexerPath = path.join(__dirname, '../src/server.ts');
    if (fs.existsSync(indexerPath)) {
      console.log('✅ Indexer entry point exists');
    } else {
      console.log('❌ Indexer entry point not found');
    }
    
    // Check processor files
    const processorDir = path.join(__dirname, '../src/processors');
    const processors = [
      'engine.ts',
      'connector-created.ts',
      'bonding-curve-created.ts',
      'bonding-curve-buy.ts',
      'bonding-curve-sell.ts',
      'bonding-curve-complete.ts',
      'bonding-curve-migrate.ts'
    ];
    
    console.log('\n   Checking processor files:');
    for (const processor of processors) {
      const processorPath = path.join(processorDir, processor);
      if (fs.existsSync(processorPath)) {
        console.log(`   ✅ ${processor}`);
      } else {
        console.log(`   ❌ ${processor} - NOT FOUND`);
      }
    }
    
  } catch (error) {
    console.log(`❌ Error checking indexer files: ${error.message}`);
  }

  await prisma.$disconnect();

  console.log('\n=====================================');
  console.log('Indexer Test Complete');
  console.log('=====================================');
  
  if (tokenCount === 0) {
    console.log('\n📝 Note: No tokens found in database yet.');
    console.log('This is normal if no tokens have been created.');
    console.log('\nTo start indexing:');
    console.log('  1. Start the indexer: pnpm start');
    console.log('  2. Create a token through the frontend');
    console.log('  3. The indexer will automatically process the events');
  } else {
    console.log('\n✅ Indexer appears to be working correctly!');
    console.log(`Found ${tokenCount} tokens already indexed.`);
  }
  
  console.log('\nTo run the indexer continuously:');
  console.log('  cd apps/indexer && pnpm start');
}

testIndexer().catch(console.error);