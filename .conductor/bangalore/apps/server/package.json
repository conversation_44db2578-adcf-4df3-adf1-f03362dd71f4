{"name": "server", "type": "module", "main": "dist/server.js", "types": "dist/server.d.ts", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsup", "start": "node dist/server.js", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "compile-templates": "tsx scripts/compile-templates.ts", "lint": "eslint . --ext .ts --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit"}, "dependencies": {"@hopfun/database": "workspace:*", "@hopfun/logger": "workspace:*", "@hono/node-server": "catalog:", "@mysten/sui": "catalog:", "dotenv": "catalog:", "hono": "catalog:", "hono-rate-limiter": "catalog:", "http-status-codes": "catalog:", "ulid": "catalog:", "zod": "catalog:"}, "devDependencies": {"@hopfun/eslint": "workspace:*", "@hopfun/tsconfig": "workspace:*", "@types/node": "catalog:", "@types/supertest": "catalog:", "eslint": "catalog:", "prettier": "catalog:", "supertest": "catalog:", "tsc-alias": "catalog:", "tsup": "catalog:", "tsx": "catalog:", "typescript": "catalog:", "typescript-transform-paths": "catalog:", "vitest": "catalog:"}}