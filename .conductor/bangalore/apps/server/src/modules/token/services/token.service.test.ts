import { beforeEach, describe, expect, it, vi } from 'vitest';
import type { Token } from '@hopfun/database';

import { TokenService } from './token.service';

// Mock the database
vi.mock('@/libs/database', () => ({
  db: {
    token: {
      findUnique: vi.fn(),
      findMany: vi.fn(),
      count: vi.fn(),
    },
  },
}));

// Mock the logger
vi.mock('@hopfun/logger', () => ({
  logger: {
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
}));

const { db } = await import('@/libs/database');

describe('TokenService', () => {
  let tokenService: TokenService;

  const mockToken: Token = {
    id: 'token-id-1',
    curveId: 'curve-id-1',
    creator: '0x' + 'a'.repeat(64),
    coinName: 'Test Token',
    ticker: 'TEST',
    description: 'A test token',
    imageUrl: 'https://example.com/image.png',
    twitter: '@testtoken',
    website: 'https://testtoken.com',
    telegram: '@testtoken',
    totalSupply: '1000000000000000',
    network: 'TESTNET',
    status: 'ACTIVE',
    currentPrice: '0.001',
    marketCap: '1000000',
    virtualSuiAmount: '500000',
    suiBalance: '250000',
    tokenBalance: '750000000000000',
    availableTokenReserves: '250000000000000',
    poolId: null,
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
    completedAt: null,
    migratedAt: null,
  };

  beforeEach(() => {
    tokenService = new TokenService();
    vi.clearAllMocks();
  });

  describe('getTokenById', () => {
    it('should return token when found', async () => {
      vi.mocked(db.token.findUnique).mockResolvedValue(mockToken);

      const result = await tokenService.getTokenById('token-id-1');

      expect(result).toEqual(mockToken);
      expect(db.token.findUnique).toHaveBeenCalledWith({
        where: { id: 'token-id-1' },
        include: { stats: true },
      });
    });

    it('should return null when token not found', async () => {
      vi.mocked(db.token.findUnique).mockResolvedValue(null);

      const result = await tokenService.getTokenById('non-existent-id');

      expect(result).toBeNull();
    });

    it('should throw error when database operation fails', async () => {
      const dbError = new Error('Database connection failed');
      vi.mocked(db.token.findUnique).mockRejectedValue(dbError);

      await expect(tokenService.getTokenById('token-id-1')).rejects.toThrow(
        'Failed to fetch token: Database connection failed'
      );
    });
  });

  describe('getTokenByCurveId', () => {
    it('should return token when found', async () => {
      vi.mocked(db.token.findUnique).mockResolvedValue(mockToken);

      const result = await tokenService.getTokenByCurveId('curve-id-1');

      expect(result).toEqual(mockToken);
      expect(db.token.findUnique).toHaveBeenCalledWith({
        where: { curveId: 'curve-id-1' },
        include: { stats: true },
      });
    });

    it('should return null when token not found', async () => {
      vi.mocked(db.token.findUnique).mockResolvedValue(null);

      const result = await tokenService.getTokenByCurveId('non-existent-curve-id');

      expect(result).toBeNull();
    });

    it('should throw error when database operation fails', async () => {
      const dbError = new Error('Database connection failed');
      vi.mocked(db.token.findUnique).mockRejectedValue(dbError);

      await expect(tokenService.getTokenByCurveId('curve-id-1')).rejects.toThrow(
        'Failed to fetch token: Database connection failed'
      );
    });
  });

  describe('getTokensByCreator', () => {
    it('should return tokens for creator', async () => {
      const mockTokens = [mockToken];
      vi.mocked(db.token.findMany).mockResolvedValue(mockTokens);

      const creator = '0x' + 'a'.repeat(64);
      const result = await tokenService.getTokensByCreator(creator);

      expect(result).toEqual(mockTokens);
      expect(db.token.findMany).toHaveBeenCalledWith({
        where: { creator },
        include: { stats: true },
        orderBy: { createdAt: 'desc' },
      });
    });

    it('should return empty array when no tokens found', async () => {
      vi.mocked(db.token.findMany).mockResolvedValue([]);

      const creator = '0x' + 'b'.repeat(64);
      const result = await tokenService.getTokensByCreator(creator);

      expect(result).toEqual([]);
    });

    it('should throw error when database operation fails', async () => {
      const dbError = new Error('Database connection failed');
      vi.mocked(db.token.findMany).mockRejectedValue(dbError);

      const creator = '0x' + 'a'.repeat(64);
      await expect(tokenService.getTokensByCreator(creator)).rejects.toThrow(
        'Failed to fetch tokens: Database connection failed'
      );
    });
  });

  describe('listTokens', () => {
    it('should return paginated tokens', async () => {
      const mockTokens = [mockToken];
      const mockTotal = 1;

      vi.mocked(db.token.findMany).mockResolvedValue(mockTokens);
      vi.mocked(db.token.count).mockResolvedValue(mockTotal);

      const params = {
        page: 1,
        limit: 20,
        sortBy: 'createdAt' as const,
        sortOrder: 'desc' as const,
      };

      const result = await tokenService.listTokens(params);

      expect(result).toEqual({
        tokens: mockTokens,
        total: mockTotal,
      });

      expect(db.token.findMany).toHaveBeenCalledWith({
        where: {},
        include: { 
          stats: true,
          transactions: {
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
        },
        orderBy: { createdAt: 'desc' },
        skip: 0,
        take: 20,
      });

      expect(db.token.count).toHaveBeenCalledWith({ where: {} });
    });

    it('should apply filters correctly', async () => {
      const mockTokens = [mockToken];
      const mockTotal = 1;

      vi.mocked(db.token.findMany).mockResolvedValue(mockTokens);
      vi.mocked(db.token.count).mockResolvedValue(mockTotal);

      const params = {
        page: 2,
        limit: 10,
        network: 'TESTNET' as const,
        status: 'ACTIVE' as const,
        sortBy: 'marketCap' as const,
        sortOrder: 'asc' as const,
      };

      await tokenService.listTokens(params);

      expect(db.token.findMany).toHaveBeenCalledWith({
        where: { network: 'TESTNET', status: 'ACTIVE' },
        include: { 
          stats: true,
          transactions: {
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
        },
        orderBy: { marketCap: 'asc' },
        skip: 10,
        take: 10,
      });

      expect(db.token.count).toHaveBeenCalledWith({
        where: { network: 'TESTNET', status: 'ACTIVE' },
      });
    });

    it('should apply search filter correctly', async () => {
      const mockTokens = [mockToken];
      const mockTotal = 1;

      vi.mocked(db.token.findMany).mockResolvedValue(mockTokens);
      vi.mocked(db.token.count).mockResolvedValue(mockTotal);

      const params = {
        page: 1,
        limit: 20,
        search: 'test',
        sortBy: 'createdAt' as const,
        sortOrder: 'desc' as const,
      };

      await tokenService.listTokens(params);

      expect(db.token.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            OR: [
              { coinName: { contains: 'test', mode: 'insensitive' } },
              { ticker: { contains: 'test', mode: 'insensitive' } },
              { description: { contains: 'test', mode: 'insensitive' } },
            ],
          },
        }),
      );
    });

    it('should apply new tokens filter correctly', async () => {
      const mockTokens = [mockToken];
      const mockTotal = 1;

      vi.mocked(db.token.findMany).mockResolvedValue(mockTokens);
      vi.mocked(db.token.count).mockResolvedValue(mockTotal);

      const params = {
        page: 1,
        limit: 20,
        filter: 'new' as const,
        sortBy: 'createdAt' as const,
        sortOrder: 'desc' as const,
      };

      await tokenService.listTokens(params);

      expect(db.token.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            createdAt: expect.objectContaining({
              gte: expect.any(Date),
            }),
          },
        }),
      );
    });

    it('should apply graduating filter correctly', async () => {
      const mockTokens = [mockToken];
      const mockTotal = 1;

      vi.mocked(db.token.findMany).mockResolvedValue(mockTokens);
      vi.mocked(db.token.count).mockResolvedValue(mockTotal);

      const params = {
        page: 1,
        limit: 20,
        filter: 'graduating' as const,
        sortBy: 'createdAt' as const,
        sortOrder: 'desc' as const,
      };

      await tokenService.listTokens(params);

      expect(db.token.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            marketCap: { gte: '60000000000000' },
            status: 'ACTIVE',
          },
        }),
      );
    });

    it('should filter by heatingUp correctly', async () => {
      const mockTokens = [
        { ...mockToken, stats: { transactions24h: 15 } },
        { ...mockToken, id: 'token-2', stats: { transactions24h: 5 } },
      ];

      vi.mocked(db.token.findMany).mockResolvedValue(mockTokens as any);
      vi.mocked(db.token.count).mockResolvedValue(2);

      const params = {
        page: 1,
        limit: 20,
        filter: 'heatingUp' as const,
        sortBy: 'createdAt' as const,
        sortOrder: 'desc' as const,
      };

      const result = await tokenService.listTokens(params);

      // Should only return tokens with more than 10 transactions in 24h
      expect(result.tokens).toHaveLength(1);
      expect(result.tokens[0].id).toBe('token-id-1');
    });

    it('should apply market cap range filters correctly', async () => {
      const mockTokens = [mockToken];
      const mockTotal = 1;

      vi.mocked(db.token.findMany).mockResolvedValue(mockTokens);
      vi.mocked(db.token.count).mockResolvedValue(mockTotal);

      const params = {
        page: 1,
        limit: 20,
        minMarketCap: 100000,
        maxMarketCap: 5000000,
        sortBy: 'marketCap' as const,
        sortOrder: 'asc' as const,
      };

      await tokenService.listTokens(params);

      expect(db.token.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            marketCap: {
              gte: '100000',
              lte: '5000000',
            },
          },
        }),
      );
    });

    it('should apply date range filters correctly', async () => {
      const mockTokens = [mockToken];
      const mockTotal = 1;

      vi.mocked(db.token.findMany).mockResolvedValue(mockTokens);
      vi.mocked(db.token.count).mockResolvedValue(mockTotal);

      const params = {
        page: 1,
        limit: 20,
        createdAfter: '2024-01-01T00:00:00Z',
        createdBefore: '2024-12-31T23:59:59Z',
        sortBy: 'createdAt' as const,
        sortOrder: 'desc' as const,
      };

      await tokenService.listTokens(params);

      expect(db.token.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            createdAt: {
              gte: new Date('2024-01-01T00:00:00Z'),
              lte: new Date('2024-12-31T23:59:59Z'),
            },
          },
        }),
      );
    });

    it('should throw error when database operation fails', async () => {
      const dbError = new Error('Database connection failed');
      vi.mocked(db.token.findMany).mockRejectedValue(dbError);

      const params = {
        page: 1,
        limit: 20,
        sortBy: 'createdAt' as const,
        sortOrder: 'desc' as const,
      };

      await expect(tokenService.listTokens(params)).rejects.toThrow(
        'Failed to list tokens: Database connection failed'
      );
    });
  });

  describe('verifyTokenExists', () => {
    it('should return true when token exists', async () => {
      vi.mocked(db.token.findUnique).mockResolvedValue({ id: 'token-id-1' } as Token);

      const result = await tokenService.verifyTokenExists('curve-id-1');

      expect(result).toBe(true);
      expect(db.token.findUnique).toHaveBeenCalledWith({
        where: { curveId: 'curve-id-1' },
        select: { id: true },
      });
    });

    it('should return false when token does not exist', async () => {
      vi.mocked(db.token.findUnique).mockResolvedValue(null);

      const result = await tokenService.verifyTokenExists('non-existent-curve-id');

      expect(result).toBe(false);
    });

    it('should throw error when database operation fails', async () => {
      const dbError = new Error('Database connection failed');
      vi.mocked(db.token.findUnique).mockRejectedValue(dbError);

      await expect(tokenService.verifyTokenExists('curve-id-1')).rejects.toThrow(
        'Failed to verify token existence: Database connection failed'
      );
    });
  });

  describe('verifyToken', () => {
    it('should return valid verification when token exists and matches criteria', async () => {
      const mockTokenData = {
        id: mockToken.id,
        curveId: mockToken.curveId,
        creator: mockToken.creator,
        coinName: mockToken.coinName,
        ticker: mockToken.ticker,
        status: mockToken.status,
        network: mockToken.network,
        createdAt: mockToken.createdAt,
      };

      vi.mocked(db.token.findUnique).mockResolvedValue(mockTokenData as Token);

      const params = {
        curveId: 'curve-id-1',
        creator: '0x' + 'a'.repeat(64),
        ticker: 'TEST',
      };

      const result = await tokenService.verifyToken(params);

      expect(result).toEqual({
        exists: true,
        isValid: true,
        token: {
          id: mockToken.id,
          curveId: mockToken.curveId,
          creator: mockToken.creator,
          coinName: mockToken.coinName,
          ticker: mockToken.ticker,
          status: mockToken.status,
          network: mockToken.network,
          createdAt: mockToken.createdAt.toISOString(),
        },
      });
    });

    it('should return invalid verification when token exists but does not match criteria', async () => {
      const mockTokenData = {
        id: mockToken.id,
        curveId: mockToken.curveId,
        creator: mockToken.creator,
        coinName: mockToken.coinName,
        ticker: mockToken.ticker,
        status: mockToken.status,
        network: mockToken.network,
        createdAt: mockToken.createdAt,
      };

      vi.mocked(db.token.findUnique).mockResolvedValue(mockTokenData as Token);

      const params = {
        curveId: 'curve-id-1',
        creator: '0x' + 'b'.repeat(64), // Different creator
        ticker: 'WRONG', // Different ticker
      };

      const result = await tokenService.verifyToken(params);

      expect(result).toEqual({
        exists: true,
        isValid: false,
        token: {
          id: mockToken.id,
          curveId: mockToken.curveId,
          creator: mockToken.creator,
          coinName: mockToken.coinName,
          ticker: mockToken.ticker,
          status: mockToken.status,
          network: mockToken.network,
          createdAt: mockToken.createdAt.toISOString(),
        },
        validationErrors: ['Creator address does not match', 'Ticker does not match'],
      });
    });

    it('should return not exists when token does not exist', async () => {
      vi.mocked(db.token.findUnique).mockResolvedValue(null);

      const params = {
        curveId: 'non-existent-curve-id',
      };

      const result = await tokenService.verifyToken(params);

      expect(result).toEqual({
        exists: false,
        isValid: false,
        validationErrors: ['Token does not exist'],
      });
    });

    it('should throw error when database operation fails', async () => {
      const dbError = new Error('Database connection failed');
      vi.mocked(db.token.findUnique).mockRejectedValue(dbError);

      const params = {
        curveId: 'curve-id-1',
      };

      await expect(tokenService.verifyToken(params)).rejects.toThrow(
        'Failed to verify token: Database connection failed'
      );
    });
  });
});
