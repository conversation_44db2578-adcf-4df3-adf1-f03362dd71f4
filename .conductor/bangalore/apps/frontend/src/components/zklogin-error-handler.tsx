'use client';

import { <PERSON><PERSON>Circle, RefreshCw, Shield } from 'lucide-react';
import { useState } from 'react';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  clearWalletCache,
  clearWalletSessionStorage,
} from '@/utils/wallet-reset';

interface ZKLoginErrorHandlerProps {
  error: Error | null;
  onRetry?: () => void;
}

export function ZKLoginErrorHandler({ error, onRetry }: ZKLoginErrorHandlerProps) {
  const [isClearing, setIsClearing] = useState(false);

  if (!error) return null;

  const errorMessage = error.message || '';
  const isZKLoginError = errorMessage.includes('ZKLogin max epoch too large') ||
    errorMessage.includes('Invalid user signature') ||
    errorMessage.includes('Signature is not valid');

  if (!isZKLoginError) return null;

  // Parse epoch information from error
  const epochMatch = errorMessage.match(
    /max epoch too large (\d+), current epoch (\d+), max accepted: (\d+)/
  );
  
  let maxEpoch = 0;
  let currentEpoch = 0;
  let maxAccepted = 0;
  
  if (epochMatch) {
    [, maxEpoch, currentEpoch, maxAccepted] = epochMatch.map(Number);
  }

  const handleClearCache = async () => {
    setIsClearing(true);
    try {
      // Clear all wallet caches
      clearWalletCache();
      clearWalletSessionStorage();
      
      // Wait a bit for the cache to clear
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Show success message
      alert('Wallet cache cleared! Please reconnect your wallet.');
      
      // Reload the page to ensure clean state
      window.location.reload();
    } catch (err) {
      console.error('Failed to clear cache:', err);
      alert('Failed to clear cache. Please try manually clearing your browser cache.');
    } finally {
      setIsClearing(false);
    }
  };

  return (
    <Card className="border-red-200 bg-red-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-red-800">
          <Shield className="h-5 w-5" />
          ZKLogin Authentication Error
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert className="border-red-200">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertTitle>Invalid ZKLogin Signature</AlertTitle>
          <AlertDescription className="mt-2 space-y-2">
            <p>Your wallet is using an outdated or invalid ZKLogin signature.</p>
            {epochMatch && (
              <div className="mt-2 rounded bg-red-100 p-2 text-xs">
                <p><strong>Technical Details:</strong></p>
                <p>• Current Network Epoch: {currentEpoch}</p>
                <p>• Your Wallet's Max Epoch: {maxEpoch}</p>
                <p>• Maximum Allowed: {maxAccepted} (current + 30)</p>
                <p className="mt-1 text-red-700">
                  Your wallet is trying to use epoch {maxEpoch}, which is {maxEpoch - currentEpoch} epochs in the future.
                  This exceeds the allowed limit of 30 epochs.
                </p>
              </div>
            )}
          </AlertDescription>
        </Alert>

        <div className="space-y-3">
          <h4 className="font-semibold text-sm">How to Fix:</h4>
          
          <div className="space-y-2 text-sm">
            <div className="flex items-start gap-2">
              <span className="font-semibold text-red-600">1.</span>
              <div>
                <p className="font-medium">Clear Wallet Cache (Recommended)</p>
                <Button
                  onClick={handleClearCache}
                  disabled={isClearing}
                  size="sm"
                  variant="outline"
                  className="mt-1"
                >
                  {isClearing ? (
                    <>
                      <RefreshCw className="mr-2 h-3 w-3 animate-spin" />
                      Clearing...
                    </>
                  ) : (
                    'Clear Cache & Reload'
                  )}
                </Button>
              </div>
            </div>
            
            <div className="flex items-start gap-2">
              <span className="font-semibold text-red-600">2.</span>
              <div>
                <p className="font-medium">Manual Fix (if above doesn't work)</p>
                <ul className="ml-4 mt-1 list-disc text-xs text-gray-600">
                  <li>Disconnect your wallet completely</li>
                  <li>Clear your browser cache (Cmd+Shift+Delete on Mac, Ctrl+Shift+Delete on Windows)</li>
                  <li>Close and reopen your browser</li>
                  <li>Reconnect your wallet to the app</li>
                </ul>
              </div>
            </div>
            
            <div className="flex items-start gap-2">
              <span className="font-semibold text-red-600">3.</span>
              <div>
                <p className="font-medium">Switch Wallets (Alternative)</p>
                <p className="text-xs text-gray-600 mt-1">
                  If you're using a ZKLogin wallet (Google/Facebook login), try switching to a different wallet provider like Sui Wallet or Suiet.
                </p>
              </div>
            </div>
          </div>
        </div>

        {onRetry && (
          <Button
            onClick={onRetry}
            className="w-full"
            variant="default"
          >
            Retry Transaction
          </Button>
        )}

        <div className="rounded bg-blue-50 border border-blue-200 p-3 text-xs">
          <p className="font-semibold text-blue-800">Why does this happen?</p>
          <p className="mt-1 text-blue-700">
            ZKLogin uses cryptographic proofs that are valid for a limited time (measured in epochs). 
            Your wallet cached a proof that's too far in the future. Sui network only accepts proofs 
            that are valid for up to 30 epochs from the current epoch to prevent replay attacks.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}