/**
 * Utility functions to help reset wallet state and clear cached data
 * This is particularly useful for resolving ZKLogin signature issues
 */

/**
 * Clear all wallet-related data from localStorage
 * This helps resolve ZKLogin signature caching issues
 */
export function clearWalletCache(): void {
  try {
    // Clear dapp-kit specific storage
    localStorage.removeItem('sui-dapp-kit:wallet-connection-info');
    localStorage.removeItem('sui-dapp-kit:last-connected-wallet-name');
    localStorage.removeItem('sui-dapp-kit:auto-connect-enabled');

    // Clear any other wallet-related storage
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (
        key &&
        (key.includes('wallet') ||
          key.includes('zklogin') ||
          key.includes('sui-') ||
          key.includes('dapp-kit'))
      ) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach((key) => localStorage.removeItem(key));

    console.log('Wallet cache cleared successfully');
  } catch (error) {
    console.error('Failed to clear wallet cache:', error);
  }
}

/**
 * Clear sessionStorage wallet data
 */
export function clearWalletSessionStorage(): void {
  try {
    const keysToRemove: string[] = [];
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (
        key &&
        (key.includes('wallet') ||
          key.includes('zklogin') ||
          key.includes('sui-') ||
          key.includes('dapp-kit'))
      ) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach((key) => sessionStorage.removeItem(key));

    console.log('Wallet session storage cleared successfully');
  } catch (error) {
    console.error('Failed to clear wallet session storage:', error);
  }
}

/**
 * Complete wallet reset - clears all cached data
 */
export function resetWallet(): void {
  clearWalletCache();
  clearWalletSessionStorage();

  // Force a page reload to ensure clean state
  if (typeof window !== 'undefined') {
    window.location.reload();
  }
}

/**
 * Check if the current error is related to ZKLogin signature issues
 */
export function isZKLoginSignatureError(error: any): boolean {
  const errorMessage = error?.message || error?.details?.message || '';

  return (
    errorMessage.includes('ZKLogin max epoch too large') ||
    errorMessage.includes('Invalid user signature') ||
    errorMessage.includes('Signature is not valid')
  );
}

/**
 * Get user-friendly instructions for resolving ZKLogin issues
 */
export function getZKLoginErrorInstructions(): string {
  return `
To fix this ZKLogin signature issue:

1. Disconnect your wallet
2. Clear browser cache (or use the "Reset Wallet" button)
3. Reconnect your wallet
4. Make sure your wallet is connected to the same network as the app (devnet)
5. Try the transaction again

This error typically occurs when your wallet has cached an outdated signature or is connected to a different network.
  `.trim();
}

/**
 * Check if there's a potential network mismatch causing ZKLogin issues
 */
export function detectNetworkMismatch(error: any): boolean {
  const errorMessage = error?.message || error?.details?.message || '';

  // ZKLogin epoch errors often indicate network mismatches
  if (errorMessage.includes('ZKLogin max epoch too large')) {
    // Extract epoch numbers from error message
    const epochMatch = errorMessage.match(
      /max epoch too large (\d+), current epoch (\d+), max accepted: (\d+)/,
    );
    if (epochMatch) {
      const [, maxEpoch, currentEpoch, maxAccepted] = epochMatch.map(Number);

      // If the difference is very large (>100), it's likely a network mismatch
      const epochDifference = maxEpoch - currentEpoch;
      if (epochDifference > 100) {
        console.warn('Potential network mismatch detected:', {
          maxEpoch,
          currentEpoch,
          epochDifference,
          maxAccepted,
        });
        return true;
      }
    }
  }

  return false;
}

/**
 * Get network-specific error message
 */
export function getNetworkMismatchMessage(): string {
  return `
Network mismatch detected!

Your wallet appears to be connected to a different network than the application.

Please ensure:
1. Your wallet is connected to DEVNET
2. The application is configured for DEVNET
3. Both are using the same RPC endpoints

Try disconnecting and reconnecting your wallet to the correct network.
  `.trim();
}
