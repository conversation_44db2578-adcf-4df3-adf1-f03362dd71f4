{"bytecode": "oRzrCwYAAAALAQAWAhY8A1JNBJ8BEAWvAbEBB+AChQMI5QWgAQaFB2YK6wcbC4YIAgyICHkAJAQeAw4BHAEhAgwCDQIbAiUCJwIoAAcCAAAECAEAAQEDCAADBQcBAAAEBgcABQAEAQABBgEMAQABBgIMAQABBggMAQABBwoEAAkJAgAKCwcAABUAAQAADwIBAAIZGAEBAAMgBgcBAAQpBBcABhAJCgECBhYNEgEABhcMDQEABxIRAQAHGBARAAgdBgEBDAglFAEBCAkfDg8AChoEBQADBQUICgsHCAYICxMKFgIIAggABwgKAAMLAQEIAAYIAgcICgQFCwcBCAALBgEIAAsIAQgAAQoCAQgLAQkAAQsDAQkAAQgABwkAAgoCCgIKAgsDAQgLBwgKAgsIAQkACwcBCQABCwcBCAADBwsIAQkAAwcICgELBgEJAAEGCAoBBQEHCAoBCAkBCwUBCQABCwEBCAACCQAFBAULBQEIAAMLCAEIAAELCAEIAAEIBAgDCwUBCQAIBAgECAQFBggCBwgKB0JhbGFuY2UEQ29pbgxDb2luTWV0YWRhdGEOQ29uZmlnUmVnaXN0cnkOQ3VycmVuY3lIb2xkZXIGT3B0aW9uBlN0cmluZwhURU1QTEFURQtUcmVhc3VyeUNhcAlUeENvbnRleHQDVUlEA1VybAdiYWxhbmNlBGNvaW4JY29ubmVjdG9yEGNyZWF0ZV9jb25uZWN0b3IPY3JlYXRlX2N1cnJlbmN5B2NyZWF0b3IGZGVsZXRlC2R1bW15X2ZpZWxkAmlkBGluaXQMaW50b19iYWxhbmNlBG1pbnQDbmV3D25ld19mcm9tX3N1cHBseRVuZXdfdW5zYWZlX2Zyb21fYnl0ZXMGb2JqZWN0Bm9wdGlvbhRwdWJsaWNfZnJlZXplX29iamVjdAhyZWdpc3RyeQZzZW5kZXIEc29tZQZzdHJpbmcGc3VwcGx5B3RlbXBfaWQIdGVtcGxhdGUIdHJhbnNmZXIMdHJlYXN1cnlfY2FwCnR4X2NvbnRleHQDdXJsBHV0ZjgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAJJf/J2myhmTQUmARMlQqKnfnUzmUXcNKSU5A/NaABlrJpSB7Vk9+iweU4ETrgb/OMcsp0sO5TpTrMSDEXxrmiyCgIJCEltYWdlVXJsAgEGCgIIB1R3aXR0ZXIKAggHV2Vic2l0ZQoCCQhUZWxlZ3JhbQoCBQROYW1lCgIHBlN5bWJvbAoCDAtEZXNjcmlwdGlvbgMIewAAAAAAAAADCACAxqR+jQMAAAIBEwEBAgUUCAkmCwgBCQAiCwUBCQAjAxEFAQgAAAAAAyILAAcBBwYHBQcHBwARDTgACgE4AQwDDAULAzgCDQUHCQoBOAMMBAoBLhEMDAILAREJCwULBDgEBwgKAjkACwI4BQIBAQQAFRYLADoADAMMBQwEDAYRCAsGOAYLBQsEBwIRBAcDEQQHBBEECwMLAQsCOAcCAA==", "dependencies": ["0x0000000000000000000000000000000000000000000000000000000000000001", "0x0000000000000000000000000000000000000000000000000000000000000002", "0x9a5207b564f7e8b0794e044eb81bfce31cb29d2c3b94e94eb3120c45f1ae68b2", "0x5055ea61493f3555c493d010bb724c57e432ceedff5d2b9598c82013b4fc97bf", "0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac"], "metadata": {"name": "TEMPLATE", "symbol": "TEMPLATE", "description": "Template coin with registry support", "iconUrl": "ImageUrl", "totalSupply": "1000000000000000", "decimals": 6, "usesRegistry": true, "version": "2.0.0"}, "checksum": "==", "generatedAt": "2025-08-13T07:28:51.138Z"}