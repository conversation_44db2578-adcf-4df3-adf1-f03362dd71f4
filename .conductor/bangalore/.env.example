# MongoDB Configuration
# =====================

# MongoDB Root Credentials (CHANGE IN PRODUCTION!)
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=changeme_in_production

# MongoDB Database Configuration
MONGO_DATABASE=hopfun
MONGO_REPLICA_SET=rs0
MONGODB_PORT=27017

# MongoDB Performance Tuning
MONGO_CACHE_SIZE_GB=1
MONGO_CPU_LIMIT=2
MONGO_MEMORY_LIMIT=2G
MONGO_CPU_RESERVATION=0.5
MONGO_MEMORY_RESERVATION=512M

# MongoDB Express (Web UI) Configuration
# =======================================
# Note: Only enable in development environments
MONGO_EXPRESS_PORT=8081
MONGO_EXPRESS_USERNAME=admin
MONGO_EXPRESS_PASSWORD=changeme_in_production
MONGO_EXPRESS_ENABLE_ADMIN=true
MONGO_EXPRESS_THEME=ambiance

# MongoDB Monitoring
# ==================
MONGO_EXPORTER_PORT=9216

# Backup Configuration
# ====================
BACKUP_RETENTION_DAYS=7
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM

# Application Database URLs
# =========================
# For MongoDB Compass: *************************************************************************
# For Applications: *******************************************************************************
DATABASE_URL=*******************************************************************************

# Network Configuration
# =====================
NETWORK=TESTNET

# Environment
# ===========
NODE_ENV=development