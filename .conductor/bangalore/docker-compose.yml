services:
  # MongoDB Primary Instance
  mongodb:
    image: mongo:7.0
    container_name: hopfun-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-password}
      MONGO_INITDB_DATABASE: ${MONGO_DATABASE:-hopfun}
    volumes:
      # Data persistence
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
      # Initialization scripts
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/01-init.js:ro
      # Keyfile for replica set
      - ./scripts/mongodb-keyfile:/etc/mongodb-keyfile:ro
      # Backup directory
      - ./backups/mongodb:/backups
    command: ["--replSet", "rs0", "--bind_ip_all", "--keyFile", "/etc/mongodb-keyfile"]
    networks:
      - hopfun-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 20s

  # Replica set initialization service
  mongodb-setup:
    image: mongo:7.0
    container_name: hopfun-mongodb-setup
    restart: "no"
    depends_on:
      mongodb:
        condition: service_healthy
    environment:
      MONGO_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-password}
      MONGO_REPLICA_SET_NAME: ${MONGO_REPLICA_SET:-rs0}
    volumes:
      - ./scripts/rs-init-simple.sh:/scripts/rs-init.sh:ro
    networks:
      - hopfun-network
    entrypoint: ["bash", "/scripts/rs-init.sh"]

  # MongoDB Express for web-based management (development only)
  mongo-express:
    image: mongo-express:1.0.2
    container_name: hopfun-mongo-express
    restart: unless-stopped
    ports:
      - "${MONGO_EXPRESS_PORT:-8081}:8081"
    environment:
      ME_CONFIG_MONGODB_SERVER: mongodb
      ME_CONFIG_MONGODB_PORT: 27017
      ME_CONFIG_MONGODB_ADMINUSERNAME: ${MONGO_ROOT_USERNAME:-admin}
      ME_CONFIG_MONGODB_ADMINPASSWORD: ${MONGO_ROOT_PASSWORD:-password}
      ME_CONFIG_MONGODB_URL: mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD:-password}@mongodb:27017/?authSource=admin&replicaSet=${MONGO_REPLICA_SET:-rs0}
      ME_CONFIG_BASICAUTH_USERNAME: ${MONGO_EXPRESS_USERNAME:-admin}
      ME_CONFIG_BASICAUTH_PASSWORD: ${MONGO_EXPRESS_PASSWORD:-admin}
      ME_CONFIG_MONGODB_ENABLE_ADMIN: "true"
    networks:
      - hopfun-network
    depends_on:
      mongodb:
        condition: service_healthy
    profiles:
      - dev

volumes:
  mongodb_data:
    name: hopfun_mongodb_data
  mongodb_config:
    name: hopfun_mongodb_config

networks:
  hopfun-network:
    name: hopfun_network
    driver: bridge