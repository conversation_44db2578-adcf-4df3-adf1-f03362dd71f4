generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

enum Network {
  MAINNET
  TESTNET
  DEVNET
}

enum TransactionType {
  BUY
  SELL
}

enum TokenStatus {
  ACTIVE // Trading on bonding curve
  COMPLETED // Curve completed
  MIGRATED // Migrated to DEX
}

// ============================================================================
// Raw Event Models (unchanged for compatibility)
// ============================================================================

model ConnectorCreatedEvent {
  id            String   @id @map("_id") @db.ObjectId
  connectorId   String
  coinName      String? // Store the coin type/name
  transactionId String   @unique
  createdAt     DateTime @default(now())
  package       String
  module        String
  eventType     String
  network       Network  @default(TESTNET)

  @@index([connectorId, createdAt, eventType, network])
  @@map("connector_created_events")
}

model BondingCurveCreatedEvent {
  id            String   @id @map("_id") @db.ObjectId
  curveId       String
  creator       String
  coinName      String
  ticker        String
  description   String
  imageUrl      String?
  twitter       String
  website       String
  telegram      String
  transactionId String   @unique
  createdAt     DateTime @default(now())
  package       String
  module        String
  eventType     String
  network       Network  @default(TESTNET)

  @@index([curveId, createdAt, eventType, network])
  @@index([ticker, network])
  @@index([creator, network])
  @@map("bonding_curve_created_events")
}

model BondingCurveTransaction {
  id                     String          @id @map("_id") @db.ObjectId
  curveId                String
  eventType              TransactionType
  suiAmount              String
  tokenAmount            String
  prePrice               String
  postPrice              String
  sender                 String
  isDevBuy               Boolean?
  virtualSuiAmount       String?
  postSuiBalance         String?
  postTokenBalance       String?
  availableTokenReserves String?
  transactionId          String          @unique
  createdAt              DateTime        @default(now())
  package                String
  module                 String
  network                Network         @default(TESTNET)

  @@index([curveId, eventType, sender, createdAt, network])
  @@index([sender, createdAt, network])
  @@index([eventType, createdAt, network])
  @@map("bonding_curve_transactions")
}

model BondingCurveCompleteEvent {
  id            String   @id @map("_id") @db.ObjectId
  curveId       String
  transactionId String   @unique
  createdAt     DateTime @default(now())
  package       String
  module        String
  eventType     String
  network       Network  @default(TESTNET)

  @@index([curveId, createdAt, eventType, network])
  @@map("bonding_curve_complete_events")
}

model BondingCurveMigrateEvent {
  id            String   @id @map("_id") @db.ObjectId
  curveId       String
  toPoolId      String
  transactionId String   @unique
  createdAt     DateTime @default(now())
  package       String
  module        String
  eventType     String
  network       Network  @default(TESTNET)

  @@index([curveId, createdAt, eventType, network])
  @@map("bonding_curve_migrate_events")
}

// ============================================================================
// Aggregated Models for Performance
// ============================================================================

model Token {
  id          String      @id @map("_id") @db.ObjectId
  curveId     String      @unique
  creator     String
  coinName    String
  ticker      String
  description String
  imageUrl    String?
  twitter     String
  website     String
  telegram    String
  totalSupply String
  network     Network
  status      TokenStatus @default(ACTIVE)

  // Current state
  currentPrice           String @default("0")
  marketCap              String @default("0")
  virtualSuiAmount       String @default("0")
  suiBalance             String @default("0")
  tokenBalance           String @default("0")
  availableTokenReserves String @default("0")

  // Migration info
  poolId String?

  // Timestamps
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  completedAt DateTime?
  migratedAt  DateTime?

  // Relations
  stats        TokenStats?
  holdings     UserHolding[]
  transactions TokenTransaction[]

  @@index([network, status, createdAt])
  @@index([ticker, network])
  @@index([creator, network])
  @@index([marketCap, network])
  @@index([currentPrice, network])
  @@map("tokens")
}

model TokenStats {
  id      String  @id @map("_id") @db.ObjectId
  tokenId String  @unique @db.ObjectId
  curveId String  @unique
  network Network

  // Volume stats
  volume24h   String @default("0")
  volume7d    String @default("0")
  volume30d   String @default("0")
  volumeTotal String @default("0")

  // Transaction counts
  transactions24h   Int @default(0)
  transactions7d    Int @default(0)
  transactions30d   Int @default(0)
  transactionsTotal Int @default(0)

  // Buy/Sell specific
  buyVolume24h  String @default("0")
  sellVolume24h String @default("0")
  buyCount24h   Int    @default(0)
  sellCount24h  Int    @default(0)

  // Price tracking
  priceHigh24h          String @default("0")
  priceLow24h           String @default("0")
  priceChange24h        String @default("0")
  priceChangePercent24h String @default("0")

  // Holder stats
  totalHolders     Int @default(0)
  uniqueHolders24h Int @default(0)

  lastUpdated DateTime @updatedAt

  // Relations
  token Token @relation(fields: [tokenId], references: [id])

  @@index([network, volume24h])
  @@index([network, transactions24h])
  @@map("token_stats")
}

model UserHolding {
  id          String  @id @map("_id") @db.ObjectId
  tokenId     String  @db.ObjectId
  curveId     String
  userAddress String
  network     Network

  // Holdings
  tokenAmount   String @default("0")
  avgBuyPrice   String @default("0")
  totalInvested String @default("0")
  realizedPnl   String @default("0")

  // Counters
  buyCount  Int @default(0)
  sellCount Int @default(0)

  firstBuy     DateTime?
  lastActivity DateTime  @updatedAt
  createdAt    DateTime  @default(now())

  // Relations
  token Token @relation(fields: [tokenId], references: [id])

  @@unique([tokenId, userAddress])
  @@index([userAddress, network])
  @@index([curveId, tokenAmount])
  @@map("user_holdings")
}

model TokenTransaction {
  id            String  @id @map("_id") @db.ObjectId
  tokenId       String  @db.ObjectId
  curveId       String
  transactionId String  @unique
  network       Network

  // Transaction details
  eventType   TransactionType
  suiAmount   String
  tokenAmount String
  prePrice    String
  postPrice   String
  priceImpact String          @default("0")

  // User info
  sender   String
  isDevBuy Boolean @default(false)

  // State after transaction
  virtualSuiAmount       String?
  postSuiBalance         String?
  postTokenBalance       String?
  availableTokenReserves String?

  createdAt   DateTime @default(now())
  blockHeight String?
  txDigest    String?

  // Relations
  token Token @relation(fields: [tokenId], references: [id])

  @@index([tokenId, createdAt])
  @@index([sender, network, createdAt])
  @@index([eventType, network, createdAt])
  @@index([curveId, eventType, createdAt])
  @@map("token_transactions")
}

model UserStats {
  id          String  @id @map("_id") @db.ObjectId
  userAddress String  @unique
  network     Network

  // Portfolio stats
  totalInvested    String @default("0")
  totalRealizedPnl String @default("0")
  totalTokensOwned Int    @default(0)

  // Trading stats
  totalTrades Int @default(0)
  buyCount    Int @default(0)
  sellCount   Int @default(0)

  // Volume stats
  totalVolumeTraded String @default("0")
  volume24h         String @default("0")

  firstTrade   DateTime?
  lastActivity DateTime  @updatedAt
  createdAt    DateTime  @default(now())

  @@index([network, totalVolumeTraded])
  @@index([network, totalTrades])
  @@map("user_stats")
}

// ============================================================================
// System Models
// ============================================================================

model IndexerState {
  id              String   @id @default(auto()) @map("_id") @db.ObjectId
  network         Network  @unique
  lastCheckpoint  String?
  lastEventId     String?
  lastProcessedTx String?
  isHealthy       Boolean  @default(true)
  lastError       String?
  updatedAt       DateTime @updatedAt
  createdAt       DateTime @default(now())

  @@map("indexer_state")
}

model ProcessingQueue {
  id           String    @id @map("_id") @db.ObjectId
  network      Network
  eventType    String
  eventData    Json
  attempts     Int       @default(0)
  maxAttempts  Int       @default(3)
  lastError    String?
  processed    Boolean   @default(false)
  scheduledFor DateTime  @default(now())
  createdAt    DateTime  @default(now())
  processedAt  DateTime?

  @@index([network, processed, scheduledFor])
  @@index([eventType, processed])
  @@map("processing_queue")
}
