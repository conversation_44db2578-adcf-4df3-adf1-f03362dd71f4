# Docker Compose Configuration for Production Environment
# Use: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

version: '3.8'

services:
  # MongoDB Primary - Production Configuration
  mongodb-primary:
    restart: always
    ports:
      - "127.0.0.1:27017:27017"  # Only bind to localhost
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: ${MONGO_DATABASE}
    command: [
      "mongod",
      "--config", "/etc/mongod.conf",
      "--replSet", "${MONGO_REPLICA_SET:-rs0}",
      "--bind_ip_all",
      "--keyFile", "/etc/mongodb-keyfile",
      "--wiredTigerCacheSizeGB", "${MON<PERSON><PERSON>_CACHE_SIZE_GB:-2}",
      "--journal",
      "--directoryperdb",
      "--logappend",
      "--logpath", "/var/log/mongodb/mongod.log",
      "--setParameter", "diagnosticDataCollectionEnabled=false",
      "--setParameter", "failIndexKeyTooLong=false"
    ]
    deploy:
      resources:
        limits:
          cpus: '${MONGO_CPU_LIMIT:-4}'
          memory: ${MONGO_MEMORY_LIMIT:-4G}
        reservations:
          cpus: '${MONGO_CPU_RESERVATION:-1}'
          memory: ${MONGO_MEMORY_RESERVATION:-1G}
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "10"
        compress: "true"

  # MongoDB Secondary 1 - For production replica set
  mongodb-secondary1:
    image: mongo:7.0
    container_name: hopfun-mongodb-secondary1
    hostname: mongodb-secondary1
    restart: always
    networks:
      - hopfun-network
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
    volumes:
      - mongodb_secondary1_data:/data/db
      - mongodb_secondary1_config:/data/configdb
      - ./config/mongodb/mongod.conf:/etc/mongod.conf:ro
      - ./scripts/mongodb-keyfile:/etc/mongodb-keyfile:ro
    command: [
      "mongod",
      "--config", "/etc/mongod.conf",
      "--replSet", "${MONGO_REPLICA_SET:-rs0}",
      "--bind_ip_all",
      "--keyFile", "/etc/mongodb-keyfile",
      "--wiredTigerCacheSizeGB", "${MONGO_CACHE_SIZE_GB:-2}",
      "--journal",
      "--directoryperdb"
    ]
    deploy:
      resources:
        limits:
          cpus: '${MONGO_CPU_LIMIT:-2}'
          memory: ${MONGO_MEMORY_LIMIT:-2G}
        reservations:
          cpus: '${MONGO_CPU_RESERVATION:-0.5}'
          memory: ${MONGO_MEMORY_RESERVATION:-512M}
    depends_on:
      - mongodb-primary

  # MongoDB Secondary 2 - For production replica set
  mongodb-secondary2:
    image: mongo:7.0
    container_name: hopfun-mongodb-secondary2
    hostname: mongodb-secondary2
    restart: always
    networks:
      - hopfun-network
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
    volumes:
      - mongodb_secondary2_data:/data/db
      - mongodb_secondary2_config:/data/configdb
      - ./config/mongodb/mongod.conf:/etc/mongod.conf:ro
      - ./scripts/mongodb-keyfile:/etc/mongodb-keyfile:ro
    command: [
      "mongod",
      "--config", "/etc/mongod.conf",
      "--replSet", "${MONGO_REPLICA_SET:-rs0}",
      "--bind_ip_all",
      "--keyFile", "/etc/mongodb-keyfile",
      "--wiredTigerCacheSizeGB", "${MONGO_CACHE_SIZE_GB:-2}",
      "--journal",
      "--directoryperdb"
    ]
    deploy:
      resources:
        limits:
          cpus: '${MONGO_CPU_LIMIT:-2}'
          memory: ${MONGO_MEMORY_LIMIT:-2G}
        reservations:
          cpus: '${MONGO_CPU_RESERVATION:-0.5}'
          memory: ${MONGO_MEMORY_RESERVATION:-512M}
    depends_on:
      - mongodb-primary

  # MongoDB Arbiter - For voting in replica set (low resources)
  mongodb-arbiter:
    image: mongo:7.0
    container_name: hopfun-mongodb-arbiter
    hostname: mongodb-arbiter
    restart: always
    networks:
      - hopfun-network
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
    volumes:
      - mongodb_arbiter_data:/data/db
      - ./scripts/mongodb-keyfile:/etc/mongodb-keyfile:ro
    command: [
      "mongod",
      "--replSet", "${MONGO_REPLICA_SET:-rs0}",
      "--bind_ip_all",
      "--keyFile", "/etc/mongodb-keyfile",
      "--port", "27017",
      "--smallfiles",
      "--nojournal",
      "--storageEngine", "wiredTiger",
      "--wiredTigerCacheSizeGB", "0.25"
    ]
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M
    depends_on:
      - mongodb-primary

  # Production replica set initialization
  mongodb-setup:
    environment:
      MONGO_ROOT_USERNAME: ${MONGO_ROOT_USERNAME}
      MONGO_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
      MONGO_REPLICA_SET_NAME: ${MONGO_REPLICA_SET:-rs0}
      PRODUCTION_MODE: "true"
    volumes:
      - ./scripts/rs-init-prod.sh:/scripts/rs-init.sh:ro
      - ./scripts/mongodb-users.js:/scripts/mongodb-users.js:ro

  # Disable MongoDB Express in production
  mongo-express:
    profiles:
      - never  # Never run in production

  # MongoDB Exporter - Always enabled in production
  mongodb-exporter:
    profiles: []  # Remove profile restriction
    restart: always
    ports:
      - "127.0.0.1:9216:9216"  # Only bind to localhost

  # Automated backup service with cron
  mongodb-backup-cron:
    image: mongo:7.0
    container_name: hopfun-mongodb-backup-cron
    restart: always
    networks:
      - hopfun-network
    environment:
      MONGO_ROOT_USERNAME: ${MONGO_ROOT_USERNAME}
      MONGO_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
      BACKUP_SCHEDULE: ${BACKUP_SCHEDULE:-0 2 * * *}
      BACKUP_RETENTION_DAYS: ${BACKUP_RETENTION_DAYS:-30}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      S3_BUCKET: ${S3_BACKUP_BUCKET}
    volumes:
      - ./scripts/backup-mongodb-cron.sh:/scripts/backup-mongodb-cron.sh:ro
      - ./backups/mongodb:/backups
      - /var/run/docker.sock:/var/run/docker.sock:ro
    entrypoint: ["bash", "/scripts/backup-mongodb-cron.sh"]
    depends_on:
      - mongodb-primary

volumes:
  mongodb_secondary1_data:
    name: hopfun_mongodb_secondary1_data
    driver: local
  mongodb_secondary1_config:
    name: hopfun_mongodb_secondary1_config
    driver: local
  mongodb_secondary2_data:
    name: hopfun_mongodb_secondary2_data
    driver: local
  mongodb_secondary2_config:
    name: hopfun_mongodb_secondary2_config
    driver: local
  mongodb_arbiter_data:
    name: hopfun_mongodb_arbiter_data
    driver: local