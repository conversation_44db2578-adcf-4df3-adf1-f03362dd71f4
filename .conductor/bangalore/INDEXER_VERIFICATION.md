# Indexer Event Detection Investigation & Resolution

## Problem Summary
The indexer is not detecting BondingCurveCreated events after successful token creation. This comprehensive investigation has identified and resolved multiple issues.

## Issues Found and Fixed

### 1. ✅ Package ID Configuration
**Issue**: The indexer was using the coin template package ID instead of the main HopFun package ID.

**Resolution**: Updated `HOPFUN_PACKAGE_ID` in indexer's `.env`:
```
# Correct HopFun package (where events are emitted)
HOPFUN_PACKAGE_ID=0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac

# Not the coin template package
# 0x06629ff9f0209763ca5e2a4c5fb1981577c76cffca1718e5a728929fd8dce940
```

### 2. ✅ Event Filter Bug
**Issue**: The `getAllEvents()` method was only returning `ConnectorCreated` events, not all events.

**Resolution**: Updated `apps/indexer/src/sui/events.ts`:
```typescript
// Fixed to use MoveModule filter for all events
getAllEvents(): SuiEventFilter {
  return {
    MoveModule: {
      package: this.packageId,
      module: 'events',
    },
  };
}
```

### 3. ✅ Generic Type Handling
**Issue**: Event type parser didn't handle generic type parameters in event names.

**Resolution**: Updated `getEventType()` function:
```typescript
// Now handles BondingCurveCreated<T> correctly
const cleanEventType = eventType.split('<')[0];
const eventName = cleanEventType.split('::').pop();
```

### 4. ✅ ObjectId Generation
**Issue**: Using ulid() for MongoDB _id fields that expect ObjectId format.

**Resolution**: All processors now use:
```typescript
id: new ObjectId().toString()
```

### 5. ✅ Network Configuration
**Issue**: Network enum mismatch between DEVNET in code vs TESTNET in config.

**Resolution**: Added DEVNET to Prisma schema and updated configuration.

## Current Status

### Working Components ✅
- MongoDB replica set configured and running
- Indexer connects to database successfully  
- Event processors registered correctly
- Sui client connection established
- Individual event subscriptions active
- Processing engine with retry logic ready

### Pending Verification ⚠️
- **Event Emission**: No events found from HopFun package on devnet
  - Query results return empty data
  - This could indicate:
    1. Events not being emitted in devnet
    2. Different event structure than expected
    3. Checkpoint synchronization delay

## Verification Commands

### 1. Check for Blockchain Events
```bash
# Query events from HopFun package
curl -X POST https://fullnode.devnet.sui.io \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "suix_queryEvents",
    "params": [{
      "MoveModule": {
        "package": "0x497ff2769b28664d052601132542a2a77e75339945dc34a494e40fcd680065ac",
        "module": "events"
      }
    }, null, 50, false]
  }'
```

### 2. Restart Indexer with Correct Config
```bash
# Kill any existing indexer process
pkill -f "tsx.*indexer"

# Start fresh
cd apps/indexer
pnpm dev
```

### 3. Monitor Indexer Logs
The indexer should show:
- ✅ Database connection successful
- ✅ Sui client connected
- ✅ Event subscriptions active (6 types)
- ⚠️ Waiting for events to be detected

### 4. Check Database for Indexed Data
```bash
# Using MongoDB shell
docker exec -it hopfun-mongodb-primary mongosh \
  -u admin -p password --authenticationDatabase admin \
  --eval "use hopfun; db.Token.countDocuments()"
```

## Manual Testing Approach

If events are not appearing on devnet, you can manually insert test data to verify the API integration:

```javascript
// Connect to MongoDB
docker exec -it hopfun-mongodb-primary mongosh -u admin -p password --authenticationDatabase admin

// Insert test token
use hopfun
db.Token.insertOne({
  _id: ObjectId(),
  curveId: "0xtest_curve_" + Date.now(),
  creator: "0x66a096b1eced9fa4e4c0f4bb088aa59f52ab8c3b87f2802f03e11abc0e6e8dc0",
  coinName: "Test Token",
  ticker: "TEST",
  description: "Test token for API verification",
  imageUrl: "https://example.com/image.png",
  twitter: "@testtoken",
  website: "https://testtoken.com",
  telegram: "t.me/testtoken",
  totalSupply: "1000000000000000000",
  network: "DEVNET",
  status: "ACTIVE",
  createdAt: new Date(),
  updatedAt: new Date()
})

// Create associated TokenStats
db.TokenStats.insertOne({
  _id: ObjectId(),
  tokenId: db.Token.findOne({ticker: "TEST"})._id,
  curveId: "0xtest_curve_" + Date.now(),
  network: "DEVNET",
  volumeLast24h: "0",
  transactionsLast24h: 0,
  uniqueTradersLast24h: 0,
  priceChange24h: 0,
  highPrice24h: "0",
  lowPrice24h: "0",
  totalBuyVolume: "0",
  totalSellVolume: "0",
  totalBuyCount: 0,
  totalSellCount: 0,
  holderCount: 1,
  currentPrice: "0.000001",
  currentMarketCap: "1000000",
  currentSupply: "1000000000000000000",
  availableSupply: "900000000000000000",
  bondingCurveProgress: 10,
  lastTradeAt: new Date(),
  createdAt: new Date(),
  updatedAt: new Date()
})
```

### Test API Endpoints
```bash
# List tokens
curl http://localhost:3000/api/token

# Get specific token (use the curveId from insertion)
curl http://localhost:3000/api/token/0xtest_curve_xxxxx
```

## Next Steps

1. **Verify Transaction Hash**: Get the exact transaction digest from your wallet for the token creation
2. **Query Specific Transaction**: Use the transaction digest to query for events
3. **Check Contract Code**: Verify events are being emitted in `contracts/hopfun/sources/meme.move`
4. **Consider Alternative Indexing**: If events aren't available, consider:
   - Polling object changes instead of events
   - Using transaction effects
   - Implementing a hybrid approach

## Files Updated
1. `/apps/indexer/.env` - Correct package IDs
2. `/apps/indexer/src/sui/events.ts` - Fixed event filters and type parsing  
3. `/apps/indexer/src/processors/*.ts` - ObjectId fixes
4. `/packages/database/prisma/schema.prisma` - Added DEVNET network

## Summary
The indexer infrastructure is correctly configured and ready to process events. The main outstanding issue is the absence of events from the blockchain, which may be due to:
- Network synchronization delays
- Events not being emitted in the current contract deployment
- Need for transaction finality before events appear

The manual testing approach above allows you to verify the complete data flow from database to API while investigating the event emission issue.