packages:
  - apps/*
  - packages/*
onlyBuiltDependencies:
  - "@parcel/watcher"
  - "@prisma/client"
  - "@prisma/engines"
  - "@tailwindcss/oxide"
  - esbuild
  - prisma
  - sharp
  - unrs-resolver
catalog:
  "@eslint/compat": ^1.2.2
  "@eslint/eslintrc": ^3.2.0
  "@eslint/js": ^9.24.0
  "@hono/node-server": ^1.17.1
  "@hookform/resolvers": ^5.1.1
  "@mysten/dapp-kit": ^0.17.2
  "@mysten/sui": 1.37.1
  "@next/eslint-plugin-next": 15.4.3
  "@prisma/client": ^6.13.0
  "@radix-ui/react-accordion": ^1.2.11
  "@radix-ui/react-dialog": ^1.1.14
  "@radix-ui/react-dropdown-menu": ^2.1.15
  "@radix-ui/react-label": ^2.1.7
  "@radix-ui/react-progress": ^1.1.7
  "@radix-ui/react-scroll-area": ^1.2.9
  "@radix-ui/react-select": ^2.2.5
  "@radix-ui/react-slot": ^1.2.3
  "@radix-ui/react-tabs": ^1.1.12
  "@radix-ui/react-toggle": ^1.1.9
  "@radix-ui/react-toggle-group": ^1.1.10
  "@radix-ui/react-tooltip": ^1.2.7
  "@svgr/webpack": ^8.1.0
  "@tailwindcss/postcss": ^4
  "@tanstack/react-query": ^5.84.2
  "@testing-library/dom": ^10.4.0
  "@testing-library/jest-dom": ^6.6.3
  "@testing-library/react": ^16.0.1
  "@types/eslint__eslintrc": ^2.1.2
  "@types/eslint__js": ^8.42.3
  "@types/express": ^5.0.3
  "@types/node": ^20.11.17
  "@types/react": ^19
  "@types/react-dom": ^19
  "@types/supertest": ^6.0.0
  "@typescript-eslint/eslint-plugin": 8.26.1
  "@typescript-eslint/parser": 8.26.1
  "@vitejs/plugin-react": ^4.7.0
  axios: ^1.11.0
  clsx: ^2.1.1
  date-fns: ^4.1.0
  dotenv: ^17.2.1
  eslint: ^9.31.0
  eslint-config-next: ^15.0.2
  eslint-config-prettier: ^10.1.8
  eslint-plugin-import: ^2.31.0
  eslint-plugin-jsx-a11y: ^6.10.2
  eslint-plugin-prettier: ^5.5.4
  eslint-plugin-react: ^7.37.2
  eslint-plugin-react-hooks: ^5.0.0
  eslint-plugin-simple-import-sort: ^12.1.1
  eslint-plugin-unicorn: ^58.0.0
  express: ^5.1.0
  flag-icons: ^7.5.0
  hono: ^4.8.10
  hono-rate-limiter: ^0.4.2
  http-status-codes: ^2.3.0
  jsdom: ^26.0.0
  lucide-react: ^0.525.0
  mongodb: ^6.18.0
  next: 15.4.2
  next-intl: ^4.3.4
  next-themes: ^0.4.6
  nx: 21.3.10
  p-queue: ^8.1.0
  p-retry: ^6.2.1
  pino: ^9.7.0
  pino-pretty: ^13.1.1
  pino-roll: ^1.0.1
  postcss: ^8
  prettier: ^3.6.2
  prettier-plugin-tailwindcss: ^0.6.14
  prisma: ^6.13.0
  prom-client: ^15.1.3
  react: 19.1.0
  react-dom: 19.1.0
  react-dropzone: ^14.3.8
  react-hook-form: ^7.61.1
  react-markdown: ^10.1.0
  rehype-highlight: ^7.0.2
  remark-gfm: ^4.0.1
  rimraf: 6.0.1
  sass: ^1.83.0
  sass-loader: ^16.0.4
  sonner: 2.0.7
  supertest: ^7.0.0
  tailwind-merge: ^3.3.1
  tailwind-variants: ^1.0.0
  tailwindcss: ^4
  tsc-alias: ^1.8.16
  tsup: ^8.5.0
  tsx: ^4.7.1
  tw-animate-css: ^1.3.5
  typescript: ^5.8.3
  typescript-eslint: ^8.26.1
  typescript-transform-paths: ^3.5.5
  ulid: ^3.0.1
  vite: ^7.0.5
  vitest: ^3.2.4
  zod: ^4.0.8
